{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/auth-middleware.ts"], "sourcesContent": ["// Authentication middleware for API routes\nimport { auth, currentUser } from '@clerk/nextjs/server';\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport interface AuthenticatedUser {\n  clerkId: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role?: 'admin' | 'front-desk' | 'doctor';\n  payloadUserId?: string;\n}\n\nexport interface AuthMiddlewareResult {\n  success: boolean;\n  user?: AuthenticatedUser;\n  error?: string;\n  response?: NextResponse;\n}\n\n/**\n * Middleware to validate Clerk authentication in API routes\n * Returns user data if authenticated, error response if not\n */\nexport async function validateAuthentication(): Promise<AuthMiddlewareResult> {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      return {\n        success: false,\n        error: 'Unauthorized - No user session',\n        response: NextResponse.json(\n          { error: 'Authentication required' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    // Get the current user with full profile information\n    const user = await currentUser();\n\n    if (!user) {\n      return {\n        success: false,\n        error: 'Unable to fetch user profile',\n        response: NextResponse.json(\n          { error: 'User profile not found' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    const email = user.emailAddresses?.[0]?.emailAddress;\n    const firstName = user.firstName;\n    const lastName = user.lastName;\n\n    if (!email) {\n      console.log('No email found for user:', user.id);\n      return {\n        success: false,\n        error: 'Invalid user - No email found',\n        response: NextResponse.json(\n          { error: 'User email not found' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    return {\n      success: true,\n      user: {\n        clerkId: userId,\n        email,\n        firstName: firstName || undefined,\n        lastName: lastName || undefined,\n      },\n    };\n  } catch (error) {\n    console.error('Authentication validation error:', error);\n    return {\n      success: false,\n      error: 'Authentication validation failed',\n      response: NextResponse.json(\n        { error: 'Authentication error' },\n        { status: 500 }\n      ),\n    };\n  }\n}\n\n/**\n * Create authenticated request headers for Payload CMS\n * This will include a service token or user context\n */\nexport function createPayloadHeaders(user: AuthenticatedUser): HeadersInit {\n  return {\n    'Content-Type': 'application/json',\n    'X-Clerk-User-Id': user.clerkId,\n    'X-User-Email': user.email,\n    // In production, you might want to include a service token here\n    // 'Authorization': `Bearer ${process.env.PAYLOAD_SERVICE_TOKEN}`,\n  };\n}\n\n/**\n * Enhanced error handling for API responses\n */\nexport function createErrorResponse(\n  message: string,\n  status: number = 500,\n  details?: any\n): NextResponse {\n  const errorResponse = {\n    error: message,\n    timestamp: new Date().toISOString(),\n    ...(details && { details }),\n  };\n\n  console.error('API Error:', errorResponse);\n  return NextResponse.json(errorResponse, { status });\n}\n\n/**\n * Success response helper\n */\nexport function createSuccessResponse(data: any, status: number = 200): NextResponse {\n  return NextResponse.json(data, { status });\n}\n\n/**\n * Wrapper function for API route handlers with authentication\n * Overloaded to support both regular routes and dynamic routes with params\n */\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest) => Promise<NextResponse>\n): (request: NextRequest) => Promise<NextResponse>;\n\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest, context: any) => Promise<NextResponse>\n): (request: NextRequest, context: any) => Promise<NextResponse>;\n\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest, context?: any) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, context?: any): Promise<NextResponse> => {\n    const authResult = await validateAuthentication();\n\n    if (!authResult.success) {\n      return authResult.response!;\n    }\n\n    try {\n      if (context) {\n        return await handler(authResult.user!, request, context);\n      } else {\n        return await handler(authResult.user!, request);\n      }\n    } catch (error) {\n      console.error('API handler error:', error);\n      return createErrorResponse('Internal server error');\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;AAC3C;AAAA;AACA;;;AAsBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,+OAAA,CAAA,eAAY,CAAC,IAAI,CACzB;oBAAE,OAAO;gBAA0B,GACnC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,qDAAqD;QACrD,MAAM,OAAO,MAAM,CAAA,GAAA,sSAAA,CAAA,cAAW,AAAD;QAE7B,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,+OAAA,CAAA,eAAY,CAAC,IAAI,CACzB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,MAAM,QAAQ,KAAK,cAAc,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,YAAY,KAAK,SAAS;QAChC,MAAM,WAAW,KAAK,QAAQ;QAE9B,IAAI,CAAC,OAAO;YACV,QAAQ,GAAG,CAAC,4BAA4B,KAAK,EAAE;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU,+OAAA,CAAA,eAAY,CAAC,IAAI,CACzB;oBAAE,OAAO;gBAAuB,GAChC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,SAAS;gBACT;gBACA,WAAW,aAAa;gBACxB,UAAU,YAAY;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO;YACP,UAAU,+OAAA,CAAA,eAAY,CAAC,IAAI,CACzB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAMO,SAAS,qBAAqB,IAAuB;IAC1D,OAAO;QACL,gBAAgB;QAChB,mBAAmB,KAAK,OAAO;QAC/B,gBAAgB,KAAK,KAAK;IAG5B;AACF;AAKO,SAAS,oBACd,OAAe,EACf,SAAiB,GAAG,EACpB,OAAa;IAEb,MAAM,gBAAgB;QACpB,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,GAAI,WAAW;YAAE;QAAQ,CAAC;IAC5B;IAEA,QAAQ,KAAK,CAAC,cAAc;IAC5B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,eAAe;QAAE;IAAO;AACnD;AAKO,SAAS,sBAAsB,IAAS,EAAE,SAAiB,GAAG;IACnE,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;QAAE;IAAO;AAC1C;AAcO,SAAS,mBACd,OAAgG;IAEhG,OAAO,OAAO,SAAsB;QAClC,MAAM,aAAa,MAAM;QAEzB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,WAAW,QAAQ;QAC5B;QAEA,IAAI;YACF,IAAI,SAAS;gBACX,OAAO,MAAM,QAAQ,WAAW,IAAI,EAAG,SAAS;YAClD,OAAO;gBACL,OAAO,MAAM,QAAQ,WAAW,IAAI,EAAG;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,oBAAoB;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/payload-client.ts"], "sourcesContent": ["// Payload CMS client with authentication\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport interface PayloadRequestOptions {\n  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';\n  body?: any;\n  params?: Record<string, string | number>;\n}\n\n/**\n * Enhanced Payload CMS client with proper authentication\n * Routes through frontend API routes which proxy to backend\n */\nexport class PayloadClient {\n  private user: AuthenticatedUser;\n\n  constructor(user: AuthenticatedUser) {\n    this.user = user;\n  }\n\n  /**\n   * Make authenticated request to backend API\n   * When called from server-side (API routes), goes directly to backend\n   * When called from client-side, goes through frontend API routes\n   */\n  private async makeRequest<T>(\n    endpoint: string,\n    options: PayloadRequestOptions = {}\n  ): Promise<T> {\n    const { method = 'GET', body, params } = options;\n\n    // Determine if we're running on server or client\n    const isServer = typeof window === 'undefined';\n\n    let url: string;\n    let requestOptions: RequestInit;\n\n    if (isServer) {\n      // Server-side: make direct request to backend\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n      url = `${backendUrl}/api${endpoint}`;\n\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      // Include Clerk user headers for backend authentication\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Clerk-User-Id': this.user.clerkId,\n          'X-User-Email': this.user.email,\n        },\n      };\n    } else {\n      // Client-side: use frontend API routes\n      url = `/api${endpoint}`;\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n    }\n\n    if (body && method !== 'GET') {\n      requestOptions.body = JSON.stringify(body);\n    }\n\n    try {\n      const response = await fetch(url, requestOptions);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(\n          `API request failed: ${response.status} ${response.statusText} - ${errorText}`\n        );\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Appointments API methods\n   */\n  async getAppointments(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n  }) {\n    return this.makeRequest('/appointments', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createAppointment(data: any) {\n    return this.makeRequest('/appointments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateAppointment(id: string, data: any) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patients API methods\n   */\n  async getPatients(params?: {\n    limit?: number;\n    page?: number;\n    search?: string;\n  }) {\n    const queryParams: any = { depth: '1', ...params };\n    \n    // Add search functionality\n    if (params?.search) {\n      queryParams['where[or][0][fullName][contains]'] = params.search;\n      queryParams['where[or][1][phone][contains]'] = params.search;\n      queryParams['where[or][2][email][contains]'] = params.search;\n      delete queryParams.search; // Remove search from params\n    }\n\n    return this.makeRequest('/patients', { params: queryParams });\n  }\n\n  async getPatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      params: { depth: '1' },\n    });\n  }\n\n  async createPatient(data: any) {\n    return this.makeRequest('/patients', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatient(id: string, data: any) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Treatments API methods\n   */\n  async getTreatments(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/treatments', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async getTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`);\n  }\n\n  async createTreatment(data: any) {\n    return this.makeRequest('/treatments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateTreatment(id: string, data: any) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Users API methods (for user management)\n   */\n  async getUsers(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/users', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async updateUser(userId: string, data: any) {\n    return this.makeRequest(`/users/${userId}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async syncCurrentUser() {\n    // This would sync the current Clerk user with Payload CMS\n    return this.makeRequest('/users/sync', {\n      method: 'POST',\n      body: {\n        clerkId: this.user.clerkId,\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName,\n      },\n    });\n  }\n\n  async syncUser(userData: {\n    clerkId: string;\n    email: string;\n    firstName?: string;\n    lastName?: string;\n  }) {\n    // Sync a specific user with Payload CMS and return user with role\n    try {\n      // First, try to find existing user\n      const existingUsers = await this.makeRequest<any>('/users', {\n        params: {\n          where: JSON.stringify({\n            clerkId: { equals: userData.clerkId }\n          }),\n          limit: 1,\n        },\n      });\n\n      if (existingUsers.docs && existingUsers.docs.length > 0) {\n        // Update existing user\n        const existingUser = existingUsers.docs[0];\n        const updatedUser = await this.makeRequest<any>(`/users/${existingUser.id}`, {\n          method: 'PATCH',\n          body: {\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return updatedUser;\n      } else {\n        // Create new user with default role\n        const newUser = await this.makeRequest<any>('/users', {\n          method: 'POST',\n          body: {\n            email: userData.email,\n            clerkId: userData.clerkId,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            role: 'front-desk', // Default role for new users\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return newUser;\n      }\n    } catch (error) {\n      console.error('Error syncing user with Payload:', error);\n      // Return a default user object if sync fails\n      return {\n        id: 'temp-id',\n        email: userData.email,\n        clerkId: userData.clerkId,\n        role: 'front-desk',\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n      };\n    }\n  }\n\n  /**\n   * Patient Interactions API methods\n   */\n  async getPatientInteractions(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-interactions', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientInteraction(data: any) {\n    return this.makeRequest('/patient-interactions', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientInteraction(id: string, data: any) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient Tasks API methods\n   */\n  async getPatientTasks(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-tasks', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientTask(data: any) {\n    return this.makeRequest('/patient-tasks', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientTask(id: string, data: any) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient-specific CRM methods\n   */\n  async getPatientInteractionsByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    interactionType?: string;\n    status?: string;\n    priority?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/interactions`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTasksByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    taskType?: string;\n    status?: string;\n    priority?: string;\n    assignedTo?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/tasks`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTimeline(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    type?: 'interaction' | 'task';\n  }) {\n    return this.makeRequest(`/patients/${patientId}/timeline`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n}\n\n/**\n * Factory function to create PayloadClient instance\n */\nexport function createPayloadClient(user: AuthenticatedUser): PayloadClient {\n  return new PayloadClient(user);\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAalC,MAAM;IACH,KAAwB;IAEhC,YAAY,IAAuB,CAAE;QACnC,IAAI,CAAC,IAAI,GAAG;IACd;IAEA;;;;GAIC,GACD,MAAc,YACZ,QAAgB,EAChB,UAAiC,CAAC,CAAC,EACvB;QACZ,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QAEzC,iDAAiD;QACjD,MAAM,WAAW,gBAAkB;QAEnC,IAAI;QACJ,IAAI;QAEJ,wCAAc;YACZ,8CAA8C;YAC9C,MAAM,aAAa,6DAAmC;YACtD,MAAM,GAAG,WAAW,IAAI,EAAE,UAAU;YAEpC,IAAI,QAAQ;gBACV,MAAM,eAAe,IAAI;gBACzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;wBACzC,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;oBACzC;gBACF;gBACA,IAAI,aAAa,QAAQ,IAAI;oBAC3B,OAAO,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;gBACtC;YACF;YAEA,wDAAwD;YACxD,iBAAiB;gBACf;gBACA,SAAS;oBACP,gBAAgB;oBAChB,mBAAmB,IAAI,CAAC,IAAI,CAAC,OAAO;oBACpC,gBAAgB,IAAI,CAAC,IAAI,CAAC,KAAK;gBACjC;YACF;QACF,OAAO;;QAqBP;QAEA,IAAI,QAAQ,WAAW,OAAO;YAC5B,eAAe,IAAI,GAAG,KAAK,SAAS,CAAC;QACvC;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YAElF;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;YACrD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,MAIrB,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB;YACvC,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;YAC7C,QAAQ;gBAAE,OAAO;YAAI;QACvB;IACF;IAEA,MAAM,kBAAkB,IAAS,EAAE;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB;YACvC,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,IAAS,EAAE;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;YAC7C,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;YAC7C,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,MAIjB,EAAE;QACD,MAAM,cAAmB;YAAE,OAAO;YAAK,GAAG,MAAM;QAAC;QAEjD,2BAA2B;QAC3B,IAAI,QAAQ,QAAQ;YAClB,WAAW,CAAC,mCAAmC,GAAG,OAAO,MAAM;YAC/D,WAAW,CAAC,gCAAgC,GAAG,OAAO,MAAM;YAC5D,WAAW,CAAC,gCAAgC,GAAG,OAAO,MAAM;YAC5D,OAAO,YAAY,MAAM,EAAE,4BAA4B;QACzD;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;YAAE,QAAQ;QAAY;IAC7D;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YACzC,QAAQ;gBAAE,OAAO;YAAI;QACvB;IACF;IAEA,MAAM,cAAc,IAAS,EAAE;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;YACnC,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,IAAS,EAAE;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YACzC,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YACzC,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,MAGnB,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe;YACrC,QAAQ;gBAAE,OAAO;gBAAK,GAAG,MAAM;YAAC;QAClC;IACF;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI;IAC7C;IAEA,MAAM,gBAAgB,IAAS,EAAE;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe;YACrC,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,EAAU,EAAE,IAAS,EAAE;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAC3C,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,EAAU,EAAE;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,SAAS,MAGd,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;YAChC,QAAQ;gBAAE,OAAO;gBAAK,GAAG,MAAM;YAAC;QAClC;IACF;IAEA,MAAM,WAAW,MAAc,EAAE,IAAS,EAAE;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;YAC1C,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB;QACtB,0DAA0D;QAC1D,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe;YACrC,QAAQ;YACR,MAAM;gBACJ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;gBAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;gBACtB,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS;gBAC9B,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ;YAC9B;QACF;IACF;IAEA,MAAM,SAAS,QAKd,EAAE;QACD,kEAAkE;QAClE,IAAI;YACF,mCAAmC;YACnC,MAAM,gBAAgB,MAAM,IAAI,CAAC,WAAW,CAAM,UAAU;gBAC1D,QAAQ;oBACN,OAAO,KAAK,SAAS,CAAC;wBACpB,SAAS;4BAAE,QAAQ,SAAS,OAAO;wBAAC;oBACtC;oBACA,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,IAAI,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,GAAG;gBACvD,uBAAuB;gBACvB,MAAM,eAAe,cAAc,IAAI,CAAC,EAAE;gBAC1C,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW,CAAM,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;oBAC3E,QAAQ;oBACR,MAAM;wBACJ,OAAO,SAAS,KAAK;wBACrB,WAAW,SAAS,SAAS;wBAC7B,UAAU,SAAS,QAAQ;wBAC3B,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;gBACA,OAAO;YACT,OAAO;gBACL,oCAAoC;gBACpC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAM,UAAU;oBACpD,QAAQ;oBACR,MAAM;wBACJ,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;wBACzB,WAAW,SAAS,SAAS;wBAC7B,UAAU,SAAS,QAAQ;wBAC3B,MAAM;wBACN,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,6CAA6C;YAC7C,OAAO;gBACL,IAAI;gBACJ,OAAO,SAAS,KAAK;gBACrB,SAAS,SAAS,OAAO;gBACzB,MAAM;gBACN,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;YAC7B;QACF;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,MAK5B,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,yBAAyB;YAC/C,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,sBAAsB,EAAE,IAAI,EAAE;YACrD,QAAQ;gBAAE,OAAO;YAAI;QACvB;IACF;IAEA,MAAM,yBAAyB,IAAS,EAAE;QACxC,OAAO,IAAI,CAAC,WAAW,CAAC,yBAAyB;YAC/C,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,EAAU,EAAE,IAAS,EAAE;QACpD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,sBAAsB,EAAE,IAAI,EAAE;YACrD,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,EAAU,EAAE;QACzC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,sBAAsB,EAAE,IAAI,EAAE;YACrD,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,MAKrB,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB;YACxC,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;YAC9C,QAAQ;gBAAE,OAAO;YAAI;QACvB;IACF;IAEA,MAAM,kBAAkB,IAAS,EAAE;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB;YACxC,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE,IAAS,EAAE;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;YAC9C,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,EAAU,EAAE;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;YAC9C,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,gCAAgC,SAAiB,EAAE,MAMxD,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC,EAAE;YAC7D,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;IAEA,MAAM,yBAAyB,SAAiB,EAAE,MAOjD,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAAE;YACtD,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;IAEA,MAAM,mBAAmB,SAAiB,EAAE,MAI3C,EAAE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC,EAAE;YACzD,QAAQ;gBACN,OAAO;gBACP,GAAG,MAAM;YACX;QACF;IACF;AACF;AAKO,SAAS,oBAAoB,IAAuB;IACzD,OAAO,IAAI,cAAc;AAC3B", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/api/auth/sync/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\n/**\n * Sync Clerk user with Payload CMS\n * This endpoint ensures the authenticated Clerk user exists in Payload CMS\n */\nexport const POST = withAuthentication(async (user, request: NextRequest) => {\n  try {\n    // Create authenticated Payload client\n    const payloadClient = createPayloadClient(user);\n\n    // Sync user with Payload CMS and get role information\n    const payloadUser = await payloadClient.syncUser({\n      clerkId: user.clerkId,\n      email: user.email,\n      firstName: user.firstName,\n      lastName: user.lastName,\n    });\n\n    console.log('User sync completed:', payloadUser);\n\n    return createSuccessResponse({\n      message: 'User sync completed',\n      user: {\n        clerkId: user.clerkId,\n        email: user.email,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        role: payloadUser.role,\n        payloadUserId: payloadUser.id,\n        lastLogin: new Date().toISOString(),\n      },\n    });\n  } catch (error) {\n    console.error('Error syncing user:', error);\n    return createErrorResponse('Failed to sync user');\n  }\n});\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAMO,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,MAAM;IAClD,IAAI;QACF,sCAAsC;QACtC,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE;QAE1C,sDAAsD;QACtD,MAAM,cAAc,MAAM,cAAc,QAAQ,CAAC;YAC/C,SAAS,KAAK,OAAO;YACrB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;QACzB;QAEA,QAAQ,GAAG,CAAC,wBAAwB;QAEpC,OAAO,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE;YAC3B,SAAS;YACT,MAAM;gBACJ,SAAS,KAAK,OAAO;gBACrB,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,MAAM,YAAY,IAAI;gBACtB,eAAe,YAAY,EAAE;gBAC7B,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7B;AACF", "debugId": null}}]}