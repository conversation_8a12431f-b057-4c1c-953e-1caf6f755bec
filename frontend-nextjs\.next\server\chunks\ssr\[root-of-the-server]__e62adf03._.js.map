{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/providers.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/providers.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/providers.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/providers.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_3c6d2fa1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_3c6d2fa1-module__M5xcfW__className\",\n  \"variable\": \"geist_3c6d2fa1-module__M5xcfW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_3c6d2fa1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Geist%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22fontSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_f40efb7e.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_f40efb7e-module__9VzD3G__className\",\n  \"variable\": \"geist_mono_f40efb7e-module__9VzD3G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_f40efb7e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-mono%22}],%22variableName%22:%22fontMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/instrument_sans_ea57aed0.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"instrument_sans_ea57aed0-module__mJf4-q__className\",\n  \"variable\": \"instrument_sans_ea57aed0-module__mJf4-q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/instrument_sans_ea57aed0.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Instrument_Sans%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-instrument%22}],%22variableName%22:%22fontInstrument%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Instrument Sans', 'Instrument Sans Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,+JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_mono_ac878e45.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_mono_ac878e45-module__SfBW4G__className\",\n  \"variable\": \"noto_sans_mono_ac878e45-module__SfBW4G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_mono_ac878e45.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Noto_Sans_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-noto-mono%22}],%22variableName%22:%22fontNotoMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans Mono', 'Noto Sans Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/mulish_d412d3cf.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"mulish_d412d3cf-module__oKc9Da__className\",\n  \"variable\": \"mulish_d412d3cf-module__oKc9Da__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/mulish_d412d3cf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Mulish%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-mullish%22}],%22variableName%22:%22fontMullish%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Mulish', 'Mulish Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,sJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,sJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,sJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_707c4bd4.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_707c4bd4-module__7ihonW__className\",\n  \"variable\": \"inter_707c4bd4-module__7ihonW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_707c4bd4.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22font.ts%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22fontInter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n\r\n// Date and time formatting utilities\r\nexport function formatDateTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatDate(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n  }).format(date);\r\n}\r\n\r\nexport function formatTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatRelativeTime(date: Date): string {\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) {\r\n    return '刚刚';\r\n  }\r\n\r\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n  if (diffInMinutes < 60) {\r\n    return `${diffInMinutes}分钟前`;\r\n  }\r\n\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  if (diffInHours < 24) {\r\n    return `${diffInHours}小时前`;\r\n  }\r\n\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n  if (diffInDays < 7) {\r\n    return `${diffInDays}天前`;\r\n  }\r\n\r\n  const diffInWeeks = Math.floor(diffInDays / 7);\r\n  if (diffInWeeks < 4) {\r\n    return `${diffInWeeks}周前`;\r\n  }\r\n\r\n  const diffInMonths = Math.floor(diffInDays / 30);\r\n  if (diffInMonths < 12) {\r\n    return `${diffInMonths}个月前`;\r\n  }\r\n\r\n  const diffInYears = Math.floor(diffInDays / 365);\r\n  return `${diffInYears}年前`;\r\n}\r\n\r\n// Task and interaction utilities\r\nexport function isOverdue(dueDate: string | Date): boolean {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  return due < new Date();\r\n}\r\n\r\nexport function getDaysUntilDue(dueDate: string | Date): number {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  const now = new Date();\r\n  const diffInMs = due.getTime() - now.getTime();\r\n  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));\r\n}\r\n\r\nexport function getUrgencyLevel(dueDate: string | Date, priority: string): 'low' | 'medium' | 'high' | 'urgent' {\r\n  const daysUntil = getDaysUntilDue(dueDate);\r\n\r\n  if (daysUntil < 0) return 'urgent'; // Overdue\r\n  if (daysUntil === 0) return 'urgent'; // Due today\r\n  if (daysUntil === 1) return 'high'; // Due tomorrow\r\n\r\n  if (priority === 'urgent') return 'urgent';\r\n  if (priority === 'high') return daysUntil <= 3 ? 'high' : 'medium';\r\n  if (priority === 'medium') return daysUntil <= 7 ? 'medium' : 'low';\r\n\r\n  return 'low';\r\n}\r\n\r\n// Text processing utilities\r\nexport function truncateText(text: string, maxLength: number): string {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength - 3) + '...';\r\n}\r\n\r\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, '<mark>$1</mark>');\r\n}\r\n\r\n// Validation utilities\r\nexport function isValidEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\nexport function isValidPhone(phone: string): boolean {\r\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n  return phoneRegex.test(phone.replace(/\\s/g, ''));\r\n}\r\n\r\n// Array utilities\r\nexport function groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> {\r\n  return array.reduce((groups, item) => {\r\n    const key = keyFn(item);\r\n    if (!groups[key]) {\r\n      groups[key] = [];\r\n    }\r\n    groups[key].push(item);\r\n    return groups;\r\n  }, {} as Record<string, T[]>);\r\n}\r\n\r\nexport function sortBy<T>(array: T[], keyFn: (item: T) => any, direction: 'asc' | 'desc' = 'asc'): T[] {\r\n  return [...array].sort((a, b) => {\r\n    const aVal = keyFn(a);\r\n    const bVal = keyFn(b);\r\n\r\n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\r\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\r\n    return 0;\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ;AAGO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAU;IAC3C,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,GAAG,CAAC;IAC9B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,EAAE,CAAC;IAC3B;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,GAAG,CAAC;IAC7B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,EAAE,CAAC;AAC3B;AAGO,SAAS,UAAU,OAAsB;IAC9C,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,OAAO,MAAM,IAAI;AACnB;AAEO,SAAS,gBAAgB,OAAsB;IACpD,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO;IAC5C,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AAClD;AAEO,SAAS,gBAAgB,OAAsB,EAAE,QAAgB;IACtE,MAAM,YAAY,gBAAgB;IAElC,IAAI,YAAY,GAAG,OAAO,UAAU,UAAU;IAC9C,IAAI,cAAc,GAAG,OAAO,UAAU,YAAY;IAClD,IAAI,cAAc,GAAG,OAAO,QAAQ,eAAe;IAEnD,IAAI,aAAa,UAAU,OAAO;IAClC,IAAI,aAAa,QAAQ,OAAO,aAAa,IAAI,SAAS;IAC1D,IAAI,aAAa,UAAU,OAAO,aAAa,IAAI,WAAW;IAE9D,OAAO;AACT;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,KAAK;AAC5C;AAEO,SAAS,oBAAoB,IAAY,EAAE,UAAkB;IAClE,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,QAAW,KAAU,EAAE,KAA0B;IAC/D,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,MAAM,MAAM;QAClB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,MAAM,CAAC,IAAI,GAAG,EAAE;QAClB;QACA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,KAAuB,EAAE,YAA4B,KAAK;IAC9F,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,MAAM;QACnB,MAAM,OAAO,MAAM;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/font.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>_Mono,\r\n  Instrument_Sans,\r\n  <PERSON>,\r\n  Mu<PERSON>,\r\n  Noto_Sans_Mono\r\n} from 'next/font/google';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst fontSans = Geist({\r\n  subsets: ['latin'],\r\n  variable: '--font-sans'\r\n});\r\n\r\nconst fontMono = Geist_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-mono'\r\n});\r\n\r\nconst fontInstrument = Instrument_Sans({\r\n  subsets: ['latin'],\r\n  variable: '--font-instrument'\r\n});\r\n\r\nconst fontNotoMono = Noto_Sans_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-noto-mono'\r\n});\r\n\r\nconst fontMullish = Mulish({\r\n  subsets: ['latin'],\r\n  variable: '--font-mullish'\r\n});\r\n\r\nconst fontInter = Inter({\r\n  subsets: ['latin'],\r\n  variable: '--font-inter'\r\n});\r\n\r\nexport const fontVariables = cn(\r\n  fontSans.variable,\r\n  fontMono.variable,\r\n  fontInstrument.variable,\r\n  fontNotoMono.variable,\r\n  fontMullish.variable,\r\n  fontInter.variable\r\n);\r\n"], "names": [], "mappings": ";;;;;;;;;AASA;;;;;;;;AAgCO,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC5B,yIAAA,CAAA,UAAQ,CAAC,QAAQ,EACjB,8IAAA,CAAA,UAAQ,CAAC,QAAQ,EACjB,mJAAA,CAAA,UAAc,CAAC,QAAQ,EACvB,kJAAA,CAAA,UAAY,CAAC,QAAQ,EACrB,0IAAA,CAAA,UAAW,CAAC,QAAQ,EACpB,yIAAA,CAAA,UAAS,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/ThemeToggle/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/ThemeToggle/theme-provider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/ThemeToggle/theme-provider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/ThemeToggle/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/ThemeToggle/theme-provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/ThemeToggle/theme-provider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/layout.tsx"], "sourcesContent": ["import Providers from '@/components/layout/providers';\r\nimport { Toaster } from '@/components/ui/sonner';\r\nimport { fontVariables } from '@/lib/font';\r\nimport ThemeProvider from '@/components/layout/ThemeToggle/theme-provider';\r\nimport { cn } from '@/lib/utils';\r\nimport type { Metadata, Viewport } from 'next';\r\nimport { cookies } from 'next/headers';\r\nimport NextTopLoader from 'nextjs-toploader';\r\nimport { NuqsAdapter } from 'nuqs/adapters/next/app';\r\nimport './globals.css';\r\nimport './theme.css';\r\n\r\nconst META_THEME_COLORS = {\r\n  light: '#ffffff',\r\n  dark: '#09090b'\r\n};\r\n\r\nexport const metadata: Metadata = {\r\n  title: '诊所管理系统',\r\n  description: '基于 Next.js 和 Shadcn 的医疗诊所管理系统'\r\n};\r\n\r\nexport const viewport: Viewport = {\r\n  themeColor: META_THEME_COLORS.light\r\n};\r\n\r\nexport default async function RootLayout({\r\n  children\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const cookieStore = await cookies();\r\n  const activeThemeValue = cookieStore.get('active_theme')?.value;\r\n  const isScaled = activeThemeValue?.endsWith('-scaled');\r\n\r\n  return (\r\n    <html lang='zh-CN' suppressHydrationWarning>\r\n      <head>\r\n        <script\r\n          dangerouslySetInnerHTML={{\r\n            __html: `\r\n              try {\r\n                if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {\r\n                  document.querySelector('meta[name=\"theme-color\"]').setAttribute('content', '${META_THEME_COLORS.dark}')\r\n                }\r\n              } catch (_) {}\r\n            `\r\n          }}\r\n        />\r\n      </head>\r\n      <body\r\n        className={cn(\r\n          'bg-background overflow-hidden overscroll-none font-sans antialiased',\r\n          activeThemeValue ? `theme-${activeThemeValue}` : '',\r\n          isScaled ? 'theme-scaled' : '',\r\n          fontVariables\r\n        )}\r\n      >\r\n        <NextTopLoader showSpinner={false} />\r\n        <NuqsAdapter>\r\n          <ThemeProvider\r\n            attribute='class'\r\n            defaultTheme='system'\r\n            enableSystem\r\n            disableTransitionOnChange\r\n            enableColorScheme\r\n          >\r\n            <Providers activeThemeValue={activeThemeValue as string}>\r\n              <Toaster />\r\n              {children}\r\n            </Providers>\r\n          </ThemeProvider>\r\n        </NuqsAdapter>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;AAIA,MAAM,oBAAoB;IACxB,OAAO;IACP,MAAM;AACR;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEO,MAAM,WAAqB;IAChC,YAAY,kBAAkB,KAAK;AACrC;AAEe,eAAe,WAAW,EACvC,QAAQ,EAGT;IACC,MAAM,cAAc,MAAM,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD;IAChC,MAAM,mBAAmB,YAAY,GAAG,CAAC,iBAAiB;IAC1D,MAAM,WAAW,kBAAkB,SAAS;IAE5C,qBACE,6VAAC;QAAK,MAAK;QAAQ,wBAAwB;;0BACzC,6VAAC;0BACC,cAAA,6VAAC;oBACC,yBAAyB;wBACvB,QAAQ,CAAC;;;8FAGyE,EAAE,kBAAkB,IAAI,CAAC;;;YAG3G,CAAC;oBACH;;;;;;;;;;;0BAGJ,6VAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uEACA,mBAAmB,CAAC,MAAM,EAAE,kBAAkB,GAAG,IACjD,WAAW,iBAAiB,IAC5B,kHAAA,CAAA,gBAAa;;kCAGf,6VAAC,0PAAA,CAAA,UAAa;wBAAC,aAAa;;;;;;kCAC5B,6VAAC,sQAAA,CAAA,cAAW;kCACV,cAAA,6VAAC,gKAAA,CAAA,UAAa;4BACZ,WAAU;4BACV,cAAa;4BACb,YAAY;4BACZ,yBAAyB;4BACzB,iBAAiB;sCAEjB,cAAA,6VAAC,yIAAA,CAAA,UAAS;gCAAC,kBAAkB;;kDAC3B,6VAAC,kIAAA,CAAA,UAAO;;;;;oCACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}