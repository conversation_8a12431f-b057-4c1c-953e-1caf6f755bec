// Comprehensive toast notification utilities for CRM actions
// Provides consistent messaging in Chinese for all CRM operations

import { toast } from 'sonner';
import { PatientInteraction, PatientTask, User } from '@/types/clinic';
import { formatDateTime } from '@/lib/utils';

export const crmNotifications = {
  // Patient Interaction notifications
  interaction: {
    created: (interaction: PatientInteraction) => {
      const typeLabels = {
        'phone-call': '电话通话',
        'email': '邮件沟通',
        'consultation-note': '咨询记录',
        'in-person-visit': '到院就诊',
        'treatment-discussion': '治疗讨论',
        'billing-inquiry': '账单咨询',
      };
      
      const typeLabel = typeLabels[interaction.interactionType] || '互动记录';
      toast.success(`${typeLabel}创建成功！`, {
        description: `标题: ${interaction.title}`,
        duration: 4000,
      });
    },

    updated: (interaction: PatientInteraction) => {
      toast.success(`互动记录更新成功！`, {
        description: `标题: ${interaction.title}`,
        duration: 4000,
      });
    },

    deleted: (interactionTitle: string) => {
      toast.success(`互动记录删除成功！`, {
        description: `已删除: ${interactionTitle}`,
        duration: 4000,
      });
    },

    followUpCreated: (interaction: PatientInteraction) => {
      toast.info(`跟进任务已自动创建`, {
        description: `基于互动: ${interaction.title}`,
        duration: 5000,
      });
    },

    statusChanged: (interaction: PatientInteraction, oldStatus: string, newStatus: string) => {
      const statusLabels = {
        'open': '开放',
        'in-progress': '进行中',
        'resolved': '已解决',
        'closed': '已关闭',
      };
      
      toast.success(`互动状态已更新`, {
        description: `${interaction.title}: ${statusLabels[oldStatus as keyof typeof statusLabels]} → ${statusLabels[newStatus as keyof typeof statusLabels]}`,
        duration: 4000,
      });
    },
  },

  // Patient Task notifications
  task: {
    created: (task: PatientTask) => {
      const typeLabels = {
        'follow-up-call': '跟进电话',
        'appointment-scheduling': '预约安排',
        'treatment-reminder': '治疗提醒',
        'billing-follow-up': '账单跟进',
        'medical-record-update': '病历更新',
        'consultation-follow-up': '咨询跟进',
      };
      
      const typeLabel = typeLabels[task.taskType] || '任务';
      toast.success(`${typeLabel}任务创建成功！`, {
        description: `标题: ${task.title}`,
        duration: 4000,
      });
    },

    updated: (task: PatientTask) => {
      toast.success(`任务更新成功！`, {
        description: `标题: ${task.title}`,
        duration: 4000,
      });
    },

    deleted: (taskTitle: string) => {
      toast.success(`任务删除成功！`, {
        description: `已删除: ${taskTitle}`,
        duration: 4000,
      });
    },

    assigned: (task: PatientTask, assignedTo: User) => {
      const assigneeName = `${assignedTo.firstName || ''} ${assignedTo.lastName || ''}`.trim() || assignedTo.email;
      toast.info(`任务已分配`, {
        description: `${task.title} → ${assigneeName}`,
        duration: 4000,
      });
    },

    statusChanged: (task: PatientTask, oldStatus: string, newStatus: string) => {
      const statusLabels = {
        'pending': '待处理',
        'in-progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消',
      };
      
      const statusEmojis = {
        'pending': '⏳',
        'in-progress': '🔄',
        'completed': '✅',
        'cancelled': '❌',
      };
      
      toast.success(`任务状态已更新`, {
        description: `${statusEmojis[newStatus as keyof typeof statusEmojis]} ${task.title}: ${statusLabels[oldStatus as keyof typeof statusLabels]} → ${statusLabels[newStatus as keyof typeof statusLabels]}`,
        duration: 4000,
      });
    },

    completed: (task: PatientTask) => {
      toast.success(`🎉 任务完成！`, {
        description: `${task.title} 已标记为完成`,
        duration: 5000,
      });
    },

    overdue: (task: PatientTask) => {
      toast.warning(`⚠️ 任务已逾期`, {
        description: `${task.title} - 截止时间: ${formatDateTime(new Date(task.dueDate))}`,
        duration: 8000,
      });
    },

    reminder: (task: PatientTask, minutesUntil: number) => {
      const timeText = minutesUntil < 60 
        ? `${minutesUntil} 分钟后`
        : `${Math.floor(minutesUntil / 60)} 小时后`;
        
      toast.info(`📅 任务提醒`, {
        description: `${task.title} 将在 ${timeText} 到期`,
        duration: 6000,
      });
    },
  },

  // Bulk operations
  bulk: {
    interactionsCreated: (count: number) => {
      toast.success(`批量创建互动记录成功`, {
        description: `已创建 ${count} 条互动记录`,
        duration: 4000,
      });
    },

    tasksCreated: (count: number) => {
      toast.success(`批量创建任务成功`, {
        description: `已创建 ${count} 个任务`,
        duration: 4000,
      });
    },

    tasksAssigned: (count: number, assignee: User) => {
      const assigneeName = `${assignee.firstName || ''} ${assignee.lastName || ''}`.trim() || assignee.email;
      toast.success(`批量分配任务成功`, {
        description: `已将 ${count} 个任务分配给 ${assigneeName}`,
        duration: 4000,
      });
    },

    statusUpdated: (count: number, status: string) => {
      const statusLabels = {
        'pending': '待处理',
        'in-progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消',
        'open': '开放',
        'resolved': '已解决',
        'closed': '已关闭',
      };
      
      toast.success(`批量状态更新成功`, {
        description: `${count} 个项目已更新为 ${statusLabels[status as keyof typeof statusLabels]}`,
        duration: 4000,
      });
    },
  },

  // Timeline and communication log
  timeline: {
    loaded: (count: number) => {
      toast.success(`时间线加载完成`, {
        description: `显示 ${count} 条记录`,
        duration: 2000,
      });
    },

    filtered: (totalCount: number, filteredCount: number) => {
      toast.info(`筛选结果`, {
        description: `从 ${totalCount} 条记录中筛选出 ${filteredCount} 条`,
        duration: 3000,
      });
    },

    exported: (format: string, count: number) => {
      toast.success(`导出成功`, {
        description: `已导出 ${count} 条记录为 ${format} 格式`,
        duration: 4000,
      });
    },
  },

  // Quick actions
  quickAction: {
    callInitiated: (patientName: string) => {
      toast.info(`📞 准备联系患者`, {
        description: `正在为 ${patientName} 创建通话记录`,
        duration: 3000,
      });
    },

    appointmentScheduled: (patientName: string) => {
      toast.success(`📅 预约创建成功`, {
        description: `已为 ${patientName} 创建预约`,
        duration: 4000,
      });
    },

    followUpScheduled: (patientName: string, dueDate: string) => {
      toast.success(`⏰ 跟进任务已安排`, {
        description: `${patientName} - 跟进时间: ${formatDateTime(new Date(dueDate))}`,
        duration: 5000,
      });
    },
  },

  // Error notifications
  error: {
    interactionCreateFailed: (error?: string) => {
      toast.error(`互动记录创建失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    taskCreateFailed: (error?: string) => {
      toast.error(`任务创建失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    updateFailed: (itemType: string, error?: string) => {
      const typeLabels = {
        'interaction': '互动记录',
        'task': '任务',
      };
      
      toast.error(`${typeLabels[itemType as keyof typeof typeLabels] || '项目'}更新失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    deleteFailed: (itemType: string, error?: string) => {
      const typeLabels = {
        'interaction': '互动记录',
        'task': '任务',
      };
      
      toast.error(`${typeLabels[itemType as keyof typeof typeLabels] || '项目'}删除失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    loadFailed: (dataType: string, error?: string) => {
      const typeLabels = {
        'interactions': '互动记录',
        'tasks': '任务',
        'timeline': '时间线',
      };
      
      toast.error(`${typeLabels[dataType as keyof typeof typeLabels] || '数据'}加载失败`, {
        description: error || '请检查网络连接后重试',
        duration: 5000,
      });
    },

    permissionDenied: (action: string) => {
      toast.error(`权限不足`, {
        description: `您没有权限执行: ${action}`,
        duration: 5000,
      });
    },

    networkError: () => {
      toast.error(`网络连接失败`, {
        description: '请检查网络连接后重试',
        duration: 5000,
      });
    },
  },

  // Success notifications
  success: {
    dataRefreshed: (dataType: string) => {
      const typeLabels = {
        'interactions': '互动记录',
        'tasks': '任务',
        'timeline': '时间线',
      };
      
      toast.success(`${typeLabels[dataType as keyof typeof typeLabels] || '数据'}刷新成功`, {
        duration: 2000,
      });
    },

    syncCompleted: () => {
      toast.success(`数据同步完成`, {
        description: '所有CRM数据已同步到最新状态',
        duration: 3000,
      });
    },
  },
};

// Utility function to dismiss all CRM-related toasts
export const dismissCrmToasts = () => {
  toast.dismiss();
};

// Helper function to show loading toast
export const showCrmLoadingToast = (message: string) => {
  return toast.loading(message, {
    duration: Infinity, // Will be dismissed manually
  });
};
