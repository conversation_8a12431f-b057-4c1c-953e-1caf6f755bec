// Payload CMS client with authentication
import { AuthenticatedUser } from './auth-middleware';

export interface PayloadRequestOptions {
  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';
  body?: any;
  params?: Record<string, string | number>;
}

/**
 * Enhanced Payload CMS client with proper authentication
 * Routes through frontend API routes which proxy to backend
 */
export class PayloadClient {
  private user: AuthenticatedUser;

  constructor(user: AuthenticatedUser) {
    this.user = user;
  }

  /**
   * Make authenticated request to backend API
   * When called from server-side (API routes), goes directly to backend
   * When called from client-side, goes through frontend API routes
   */
  private async makeRequest<T>(
    endpoint: string,
    options: PayloadRequestOptions = {}
  ): Promise<T> {
    const { method = 'GET', body, params } = options;

    // Determine if we're running on server or client
    const isServer = typeof window === 'undefined';

    let url: string;
    let requestOptions: RequestInit;

    if (isServer) {
      // Server-side: make direct request to backend
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
      url = `${backendUrl}/api${endpoint}`;

      if (params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        if (searchParams.toString()) {
          url += `?${searchParams.toString()}`;
        }
      }

      // Include Clerk user headers for backend authentication
      requestOptions = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-Clerk-User-Id': this.user.clerkId,
          'X-User-Email': this.user.email,
        },
      };
    } else {
      // Client-side: use frontend API routes
      url = `/api${endpoint}`;
      if (params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
          }
        });
        if (searchParams.toString()) {
          url += `?${searchParams.toString()}`;
        }
      }

      requestOptions = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `API request failed: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Appointments API methods
   */
  async getAppointments(params?: {
    limit?: number;
    page?: number;
    where?: any;
  }) {
    return this.makeRequest('/appointments', {
      params: {
        depth: '2', // Include relationships
        ...params,
      },
    });
  }

  async getAppointment(id: string) {
    return this.makeRequest(`/appointments/${id}`, {
      params: { depth: '2' },
    });
  }

  async createAppointment(data: any) {
    return this.makeRequest('/appointments', {
      method: 'POST',
      body: data,
    });
  }

  async updateAppointment(id: string, data: any) {
    return this.makeRequest(`/appointments/${id}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async deleteAppointment(id: string) {
    return this.makeRequest(`/appointments/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Patients API methods
   */
  async getPatients(params?: {
    limit?: number;
    page?: number;
    search?: string;
  }) {
    const queryParams: any = { depth: '1', ...params };
    
    // Add search functionality
    if (params?.search) {
      queryParams['where[or][0][fullName][contains]'] = params.search;
      queryParams['where[or][1][phone][contains]'] = params.search;
      queryParams['where[or][2][email][contains]'] = params.search;
      delete queryParams.search; // Remove search from params
    }

    return this.makeRequest('/patients', { params: queryParams });
  }

  async getPatient(id: string) {
    return this.makeRequest(`/patients/${id}`, {
      params: { depth: '1' },
    });
  }

  async createPatient(data: any) {
    return this.makeRequest('/patients', {
      method: 'POST',
      body: data,
    });
  }

  async updatePatient(id: string, data: any) {
    return this.makeRequest(`/patients/${id}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async deletePatient(id: string) {
    return this.makeRequest(`/patients/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Treatments API methods
   */
  async getTreatments(params?: {
    limit?: number;
    page?: number;
  }) {
    return this.makeRequest('/treatments', {
      params: { depth: '1', ...params },
    });
  }

  async getTreatment(id: string) {
    return this.makeRequest(`/treatments/${id}`);
  }

  async createTreatment(data: any) {
    return this.makeRequest('/treatments', {
      method: 'POST',
      body: data,
    });
  }

  async updateTreatment(id: string, data: any) {
    return this.makeRequest(`/treatments/${id}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async deleteTreatment(id: string) {
    return this.makeRequest(`/treatments/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Users API methods (for user management)
   */
  async getUsers(params?: {
    limit?: number;
    page?: number;
  }) {
    return this.makeRequest('/users', {
      params: { depth: '1', ...params },
    });
  }

  async updateUser(userId: string, data: any) {
    return this.makeRequest(`/users/${userId}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async syncCurrentUser() {
    // This would sync the current Clerk user with Payload CMS
    return this.makeRequest('/users/sync', {
      method: 'POST',
      body: {
        clerkId: this.user.clerkId,
        email: this.user.email,
        firstName: this.user.firstName,
        lastName: this.user.lastName,
      },
    });
  }

  async syncUser(userData: {
    clerkId: string;
    email: string;
    firstName?: string;
    lastName?: string;
  }) {
    // Sync a specific user with Payload CMS and return user with role
    try {
      // First, try to find existing user
      const existingUsers = await this.makeRequest<any>('/users', {
        params: {
          where: JSON.stringify({
            clerkId: { equals: userData.clerkId }
          }),
          limit: 1,
        },
      });

      if (existingUsers.docs && existingUsers.docs.length > 0) {
        // Update existing user
        const existingUser = existingUsers.docs[0];
        const updatedUser = await this.makeRequest<any>(`/users/${existingUser.id}`, {
          method: 'PATCH',
          body: {
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            lastLogin: new Date().toISOString(),
          },
        });
        return updatedUser;
      } else {
        // Create new user with default role
        const newUser = await this.makeRequest<any>('/users', {
          method: 'POST',
          body: {
            email: userData.email,
            clerkId: userData.clerkId,
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: 'front-desk', // Default role for new users
            lastLogin: new Date().toISOString(),
          },
        });
        return newUser;
      }
    } catch (error) {
      console.error('Error syncing user with Payload:', error);
      // Return a default user object if sync fails
      return {
        id: 'temp-id',
        email: userData.email,
        clerkId: userData.clerkId,
        role: 'front-desk',
        firstName: userData.firstName,
        lastName: userData.lastName,
      };
    }
  }

  /**
   * Patient Interactions API methods
   */
  async getPatientInteractions(params?: {
    limit?: number;
    page?: number;
    where?: any;
    sort?: string;
  }) {
    return this.makeRequest('/patient-interactions', {
      params: {
        depth: '2', // Include relationships
        ...params,
      },
    });
  }

  async getPatientInteraction(id: string) {
    return this.makeRequest(`/patient-interactions/${id}`, {
      params: { depth: '2' },
    });
  }

  async createPatientInteraction(data: any) {
    return this.makeRequest('/patient-interactions', {
      method: 'POST',
      body: data,
    });
  }

  async updatePatientInteraction(id: string, data: any) {
    return this.makeRequest(`/patient-interactions/${id}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async deletePatientInteraction(id: string) {
    return this.makeRequest(`/patient-interactions/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Patient Tasks API methods
   */
  async getPatientTasks(params?: {
    limit?: number;
    page?: number;
    where?: any;
    sort?: string;
  }) {
    return this.makeRequest('/patient-tasks', {
      params: {
        depth: '2', // Include relationships
        ...params,
      },
    });
  }

  async getPatientTask(id: string) {
    return this.makeRequest(`/patient-tasks/${id}`, {
      params: { depth: '2' },
    });
  }

  async createPatientTask(data: any) {
    return this.makeRequest('/patient-tasks', {
      method: 'POST',
      body: data,
    });
  }

  async updatePatientTask(id: string, data: any) {
    return this.makeRequest(`/patient-tasks/${id}`, {
      method: 'PATCH',
      body: data,
    });
  }

  async deletePatientTask(id: string) {
    return this.makeRequest(`/patient-tasks/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Patient-specific CRM methods
   */
  async getPatientInteractionsByPatient(patientId: string, params?: {
    limit?: number;
    page?: number;
    interactionType?: string;
    status?: string;
    priority?: string;
  }) {
    return this.makeRequest(`/patients/${patientId}/interactions`, {
      params: {
        depth: '2',
        ...params,
      },
    });
  }

  async getPatientTasksByPatient(patientId: string, params?: {
    limit?: number;
    page?: number;
    taskType?: string;
    status?: string;
    priority?: string;
    assignedTo?: string;
  }) {
    return this.makeRequest(`/patients/${patientId}/tasks`, {
      params: {
        depth: '2',
        ...params,
      },
    });
  }

  async getPatientTimeline(patientId: string, params?: {
    limit?: number;
    page?: number;
    type?: 'interaction' | 'task';
  }) {
    return this.makeRequest(`/patients/${patientId}/timeline`, {
      params: {
        depth: '2',
        ...params,
      },
    });
  }
}

/**
 * Factory function to create PayloadClient instance
 */
export function createPayloadClient(user: AuthenticatedUser): PayloadClient {
  return new PayloadClient(user);
}
