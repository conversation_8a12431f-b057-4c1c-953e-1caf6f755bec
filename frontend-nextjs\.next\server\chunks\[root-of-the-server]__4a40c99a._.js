module.exports = {

"[project]/.next-internal/server/app/api/treatments/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/auth-middleware.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Authentication middleware for API routes
__turbopack_context__.s({
    "createErrorResponse": (()=>createErrorResponse),
    "createPayloadHeaders": (()=>createPayloadHeaders),
    "createSuccessResponse": (()=>createSuccessResponse),
    "validateAuthentication": (()=>validateAuthentication),
    "withAuthentication": (()=>withAuthentication)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$currentUser$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/currentUser.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/server.js [app-route] (ecmascript)");
;
;
async function validateAuthentication() {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            return {
                success: false,
                error: 'Unauthorized - No user session',
                response: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Authentication required'
                }, {
                    status: 401
                })
            };
        }
        // Get the current user with full profile information
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$currentUser$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["currentUser"])();
        if (!user) {
            return {
                success: false,
                error: 'Unable to fetch user profile',
                response: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'User profile not found'
                }, {
                    status: 401
                })
            };
        }
        const email = user.emailAddresses?.[0]?.emailAddress;
        const firstName = user.firstName;
        const lastName = user.lastName;
        if (!email) {
            console.log('No email found for user:', user.id);
            return {
                success: false,
                error: 'Invalid user - No email found',
                response: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'User email not found'
                }, {
                    status: 401
                })
            };
        }
        return {
            success: true,
            user: {
                clerkId: userId,
                email,
                firstName: firstName || undefined,
                lastName: lastName || undefined
            }
        };
    } catch (error) {
        console.error('Authentication validation error:', error);
        return {
            success: false,
            error: 'Authentication validation failed',
            response: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication error'
            }, {
                status: 500
            })
        };
    }
}
function createPayloadHeaders(user) {
    return {
        'Content-Type': 'application/json',
        'X-Clerk-User-Id': user.clerkId,
        'X-User-Email': user.email
    };
}
function createErrorResponse(message, status = 500, details) {
    const errorResponse = {
        error: message,
        timestamp: new Date().toISOString(),
        ...details && {
            details
        }
    };
    console.error('API Error:', errorResponse);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(errorResponse, {
        status
    });
}
function createSuccessResponse(data, status = 200) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
        status
    });
}
function withAuthentication(handler) {
    return async (request, context)=>{
        const authResult = await validateAuthentication();
        if (!authResult.success) {
            return authResult.response;
        }
        try {
            if (context) {
                return await handler(authResult.user, request, context);
            } else {
                return await handler(authResult.user, request);
            }
        } catch (error) {
            console.error('API handler error:', error);
            return createErrorResponse('Internal server error');
        }
    };
}
}}),
"[project]/src/lib/payload-client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Payload CMS client with authentication
__turbopack_context__.s({
    "PayloadClient": (()=>PayloadClient),
    "createPayloadClient": (()=>createPayloadClient)
});
class PayloadClient {
    user;
    constructor(user){
        this.user = user;
    }
    /**
   * Make authenticated request to backend API
   * When called from server-side (API routes), goes directly to backend
   * When called from client-side, goes through frontend API routes
   */ async makeRequest(endpoint, options = {}) {
        const { method = 'GET', body, params } = options;
        // Determine if we're running on server or client
        const isServer = "undefined" === 'undefined';
        let url;
        let requestOptions;
        if ("TURBOPACK compile-time truthy", 1) {
            // Server-side: make direct request to backend
            const backendUrl = ("TURBOPACK compile-time value", "http://localhost:8002") || 'http://localhost:8002';
            url = `${backendUrl}/api${endpoint}`;
            if (params) {
                const searchParams = new URLSearchParams();
                Object.entries(params).forEach(([key, value])=>{
                    if (value !== undefined && value !== null) {
                        searchParams.append(key, value.toString());
                    }
                });
                if (searchParams.toString()) {
                    url += `?${searchParams.toString()}`;
                }
            }
            // Include Clerk user headers for backend authentication
            requestOptions = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Clerk-User-Id': this.user.clerkId,
                    'X-User-Email': this.user.email
                }
            };
        } else {
            "TURBOPACK unreachable";
        }
        if (body && method !== 'GET') {
            requestOptions.body = JSON.stringify(body);
        }
        try {
            const response = await fetch(url, requestOptions);
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`API request failed for ${endpoint}:`, error);
            throw error;
        }
    }
    /**
   * Appointments API methods
   */ async getAppointments(params) {
        return this.makeRequest('/appointments', {
            params: {
                depth: '2',
                ...params
            }
        });
    }
    async getAppointment(id) {
        return this.makeRequest(`/appointments/${id}`, {
            params: {
                depth: '2'
            }
        });
    }
    async createAppointment(data) {
        return this.makeRequest('/appointments', {
            method: 'POST',
            body: data
        });
    }
    async updateAppointment(id, data) {
        return this.makeRequest(`/appointments/${id}`, {
            method: 'PATCH',
            body: data
        });
    }
    async deleteAppointment(id) {
        return this.makeRequest(`/appointments/${id}`, {
            method: 'DELETE'
        });
    }
    /**
   * Patients API methods
   */ async getPatients(params) {
        const queryParams = {
            depth: '1',
            ...params
        };
        // Add search functionality
        if (params?.search) {
            queryParams['where[or][0][fullName][contains]'] = params.search;
            queryParams['where[or][1][phone][contains]'] = params.search;
            queryParams['where[or][2][email][contains]'] = params.search;
            delete queryParams.search; // Remove search from params
        }
        return this.makeRequest('/patients', {
            params: queryParams
        });
    }
    async getPatient(id) {
        return this.makeRequest(`/patients/${id}`, {
            params: {
                depth: '1'
            }
        });
    }
    async createPatient(data) {
        return this.makeRequest('/patients', {
            method: 'POST',
            body: data
        });
    }
    async updatePatient(id, data) {
        return this.makeRequest(`/patients/${id}`, {
            method: 'PATCH',
            body: data
        });
    }
    async deletePatient(id) {
        return this.makeRequest(`/patients/${id}`, {
            method: 'DELETE'
        });
    }
    /**
   * Treatments API methods
   */ async getTreatments(params) {
        return this.makeRequest('/treatments', {
            params: {
                depth: '1',
                ...params
            }
        });
    }
    async getTreatment(id) {
        return this.makeRequest(`/treatments/${id}`);
    }
    async createTreatment(data) {
        return this.makeRequest('/treatments', {
            method: 'POST',
            body: data
        });
    }
    async updateTreatment(id, data) {
        return this.makeRequest(`/treatments/${id}`, {
            method: 'PATCH',
            body: data
        });
    }
    async deleteTreatment(id) {
        return this.makeRequest(`/treatments/${id}`, {
            method: 'DELETE'
        });
    }
    /**
   * Users API methods (for user management)
   */ async getUsers(params) {
        return this.makeRequest('/users', {
            params: {
                depth: '1',
                ...params
            }
        });
    }
    async updateUser(userId, data) {
        return this.makeRequest(`/users/${userId}`, {
            method: 'PATCH',
            body: data
        });
    }
    async syncCurrentUser() {
        // This would sync the current Clerk user with Payload CMS
        return this.makeRequest('/users/sync', {
            method: 'POST',
            body: {
                clerkId: this.user.clerkId,
                email: this.user.email,
                firstName: this.user.firstName,
                lastName: this.user.lastName
            }
        });
    }
    async syncUser(userData) {
        // Sync a specific user with Payload CMS and return user with role
        try {
            // First, try to find existing user
            const existingUsers = await this.makeRequest('/users', {
                params: {
                    where: JSON.stringify({
                        clerkId: {
                            equals: userData.clerkId
                        }
                    }),
                    limit: 1
                }
            });
            if (existingUsers.docs && existingUsers.docs.length > 0) {
                // Update existing user
                const existingUser = existingUsers.docs[0];
                const updatedUser = await this.makeRequest(`/users/${existingUser.id}`, {
                    method: 'PATCH',
                    body: {
                        email: userData.email,
                        firstName: userData.firstName,
                        lastName: userData.lastName,
                        lastLogin: new Date().toISOString()
                    }
                });
                return updatedUser;
            } else {
                // Create new user with default role
                const newUser = await this.makeRequest('/users', {
                    method: 'POST',
                    body: {
                        email: userData.email,
                        clerkId: userData.clerkId,
                        firstName: userData.firstName,
                        lastName: userData.lastName,
                        role: 'front-desk',
                        lastLogin: new Date().toISOString()
                    }
                });
                return newUser;
            }
        } catch (error) {
            console.error('Error syncing user with Payload:', error);
            // Return a default user object if sync fails
            return {
                id: 'temp-id',
                email: userData.email,
                clerkId: userData.clerkId,
                role: 'front-desk',
                firstName: userData.firstName,
                lastName: userData.lastName
            };
        }
    }
    /**
   * Patient Interactions API methods
   */ async getPatientInteractions(params) {
        return this.makeRequest('/patient-interactions', {
            params: {
                depth: '2',
                ...params
            }
        });
    }
    async getPatientInteraction(id) {
        return this.makeRequest(`/patient-interactions/${id}`, {
            params: {
                depth: '2'
            }
        });
    }
    async createPatientInteraction(data) {
        return this.makeRequest('/patient-interactions', {
            method: 'POST',
            body: data
        });
    }
    async updatePatientInteraction(id, data) {
        return this.makeRequest(`/patient-interactions/${id}`, {
            method: 'PATCH',
            body: data
        });
    }
    async deletePatientInteraction(id) {
        return this.makeRequest(`/patient-interactions/${id}`, {
            method: 'DELETE'
        });
    }
    /**
   * Patient Tasks API methods
   */ async getPatientTasks(params) {
        return this.makeRequest('/patient-tasks', {
            params: {
                depth: '2',
                ...params
            }
        });
    }
    async getPatientTask(id) {
        return this.makeRequest(`/patient-tasks/${id}`, {
            params: {
                depth: '2'
            }
        });
    }
    async createPatientTask(data) {
        return this.makeRequest('/patient-tasks', {
            method: 'POST',
            body: data
        });
    }
    async updatePatientTask(id, data) {
        return this.makeRequest(`/patient-tasks/${id}`, {
            method: 'PATCH',
            body: data
        });
    }
    async deletePatientTask(id) {
        return this.makeRequest(`/patient-tasks/${id}`, {
            method: 'DELETE'
        });
    }
    /**
   * Patient-specific CRM methods
   */ async getPatientInteractionsByPatient(patientId, params) {
        return this.makeRequest(`/patients/${patientId}/interactions`, {
            params: {
                depth: '2',
                ...params
            }
        });
    }
    async getPatientTasksByPatient(patientId, params) {
        return this.makeRequest(`/patients/${patientId}/tasks`, {
            params: {
                depth: '2',
                ...params
            }
        });
    }
    async getPatientTimeline(patientId, params) {
        return this.makeRequest(`/patients/${patientId}/timeline`, {
            params: {
                depth: '2',
                ...params
            }
        });
    }
}
function createPayloadClient(user) {
    return new PayloadClient(user);
}
}}),
"[project]/src/app/api/treatments/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-middleware.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$payload$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/payload-client.ts [app-route] (ecmascript)");
;
;
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withAuthentication"])(async (user, request)=>{
    try {
        // Create authenticated Payload client
        const payloadClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$payload$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createPayloadClient"])(user);
        // Get query parameters
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        const page = parseInt(searchParams.get('page') || '1');
        // Fetch treatments with proper authentication
        const data = await payloadClient.getTreatments({
            limit,
            page
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(data);
    } catch (error) {
        console.error('Error fetching treatments:', error);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createErrorResponse"])('Failed to fetch treatments');
    }
});
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withAuthentication"])(async (user, request)=>{
    try {
        // Create authenticated Payload client
        const payloadClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$payload$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createPayloadClient"])(user);
        // Get request body
        const body = await request.json();
        // Create treatment with proper authentication
        const data = await payloadClient.createTreatment(body);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(data, 201);
    } catch (error) {
        console.error('Error creating treatment:', error);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createErrorResponse"])('Failed to create treatment');
    }
});
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4a40c99a._.js.map