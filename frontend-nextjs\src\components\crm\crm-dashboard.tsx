'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  IconTrendingUp,
  IconUsers,
  IconMessageCircle,
  IconClock,
  IconCheckCircle,
  IconAlertTriangle,
  IconCalendar,
  IconBarChart,
  IconPieChart,
  IconDownload,
  IconRefresh
} from '@/components/icons';
import { PatientInteraction, PatientTask, User } from '@/types/clinic';
import { formatDateTime, formatRelativeTime, groupBy } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface CrmDashboardProps {
  interactions: PatientInteraction[];
  tasks: PatientTask[];
  staff: User[];
  loading?: boolean;
  onRefresh?: () => void;
  onExport?: (type: string) => void;
  className?: string;
}

interface DashboardStats {
  totalInteractions: number;
  totalTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  completedTasks: number;
  activeStaff: number;
  avgResponseTime: number;
  completionRate: number;
}

interface ActivityData {
  date: string;
  interactions: number;
  tasks: number;
  completed: number;
}

export function CrmDashboard({
  interactions,
  tasks,
  staff,
  loading = false,
  onRefresh,
  onExport,
  className,
}: CrmDashboardProps) {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [stats, setStats] = useState<DashboardStats>({
    totalInteractions: 0,
    totalTasks: 0,
    pendingTasks: 0,
    overdueTasks: 0,
    completedTasks: 0,
    activeStaff: 0,
    avgResponseTime: 0,
    completionRate: 0,
  });
  const [activityData, setActivityData] = useState<ActivityData[]>([]);

  // Calculate statistics
  useEffect(() => {
    const now = new Date();
    const timeRangeMs = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
    }[timeRange] || 7 * 24 * 60 * 60 * 1000;

    const cutoffDate = new Date(now.getTime() - timeRangeMs);

    // Filter data by time range and staff
    let filteredInteractions = interactions.filter(
      item => new Date(item.timestamp) >= cutoffDate
    );
    let filteredTasks = tasks.filter(
      item => new Date(item.createdAt) >= cutoffDate
    );

    if (selectedStaff !== 'all') {
      filteredInteractions = filteredInteractions.filter(
        item => (typeof item.staffMember === 'object' ? item.staffMember.id : item.staffMember) === selectedStaff
      );
      filteredTasks = filteredTasks.filter(
        item => (typeof item.assignedTo === 'object' ? item.assignedTo.id : item.assignedTo) === selectedStaff
      );
    }

    // Calculate basic stats
    const totalInteractions = filteredInteractions.length;
    const totalTasks = filteredTasks.length;
    const pendingTasks = filteredTasks.filter(task => task.status === 'pending').length;
    const overdueTasks = filteredTasks.filter(task => 
      task.status !== 'completed' && new Date(task.dueDate) < now
    ).length;
    const completedTasks = filteredTasks.filter(task => task.status === 'completed').length;
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // Calculate active staff (staff with activity in the time range)
    const activeStaffIds = new Set([
      ...filteredInteractions.map(item => 
        typeof item.staffMember === 'object' ? item.staffMember.id : item.staffMember
      ),
      ...filteredTasks.map(item => 
        typeof item.assignedTo === 'object' ? item.assignedTo.id : item.assignedTo
      ),
    ]);
    const activeStaff = activeStaffIds.size;

    // Calculate average response time (simplified)
    const avgResponseTime = 2.5; // Placeholder - would need more complex calculation

    setStats({
      totalInteractions,
      totalTasks,
      pendingTasks,
      overdueTasks,
      completedTasks,
      activeStaff,
      avgResponseTime,
      completionRate,
    });

    // Generate activity data for charts
    const days = Math.ceil(timeRangeMs / (24 * 60 * 60 * 1000));
    const activity: ActivityData[] = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayInteractions = filteredInteractions.filter(item => 
        item.timestamp.startsWith(dateStr)
      ).length;
      
      const dayTasks = filteredTasks.filter(item => 
        item.createdAt.startsWith(dateStr)
      ).length;
      
      const dayCompleted = filteredTasks.filter(item => 
        item.completedAt && item.completedAt.startsWith(dateStr)
      ).length;

      activity.push({
        date: dateStr,
        interactions: dayInteractions,
        tasks: dayTasks,
        completed: dayCompleted,
      });
    }

    setActivityData(activity);
  }, [interactions, tasks, timeRange, selectedStaff]);

  const getInteractionsByType = () => {
    const typeGroups = groupBy(interactions, item => item.interactionType);
    return Object.entries(typeGroups).map(([type, items]) => ({
      type,
      count: items.length,
      label: {
        'phone-call': '电话通话',
        'email': '邮件沟通',
        'consultation-note': '咨询记录',
        'in-person-visit': '到院就诊',
        'treatment-discussion': '治疗讨论',
        'billing-inquiry': '账单咨询',
      }[type] || type,
    }));
  };

  const getTasksByPriority = () => {
    const priorityGroups = groupBy(tasks, item => item.priority);
    return Object.entries(priorityGroups).map(([priority, items]) => ({
      priority,
      count: items.length,
      label: {
        'low': '低',
        'medium': '中',
        'high': '高',
        'urgent': '紧急',
      }[priority] || priority,
      color: {
        'low': 'bg-gray-100 text-gray-800',
        'medium': 'bg-yellow-100 text-yellow-800',
        'high': 'bg-orange-100 text-orange-800',
        'urgent': 'bg-red-100 text-red-800',
      }[priority] || 'bg-gray-100 text-gray-800',
    }));
  };

  const getTopPerformers = () => {
    const staffPerformance = staff.map(staffMember => {
      const staffInteractions = interactions.filter(item => 
        (typeof item.staffMember === 'object' ? item.staffMember.id : item.staffMember) === staffMember.id
      );
      const staffTasks = tasks.filter(item => 
        (typeof item.assignedTo === 'object' ? item.assignedTo.id : item.assignedTo) === staffMember.id
      );
      const completedTasks = staffTasks.filter(task => task.status === 'completed');

      return {
        staff: staffMember,
        interactions: staffInteractions.length,
        tasks: staffTasks.length,
        completed: completedTasks.length,
        completionRate: staffTasks.length > 0 ? (completedTasks.length / staffTasks.length) * 100 : 0,
      };
    }).sort((a, b) => b.completionRate - a.completionRate);

    return staffPerformance.slice(0, 5);
  };

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded" />
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded" />
            <div className="h-64 bg-gray-200 rounded" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">CRM 仪表板</h2>
          <p className="text-muted-foreground">患者关系管理数据概览</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">最近7天</SelectItem>
              <SelectItem value="30d">最近30天</SelectItem>
              <SelectItem value="90d">最近90天</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedStaff} onValueChange={setSelectedStaff}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="选择员工" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有员工</SelectItem>
              {staff.map(staffMember => (
                <SelectItem key={staffMember.id} value={staffMember.id}>
                  {`${staffMember.firstName || ''} ${staffMember.lastName || ''}`.trim() || staffMember.email}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <IconRefresh className="size-4 mr-2" />
              刷新
            </Button>
          )}

          {onExport && (
            <Button variant="outline" size="sm" onClick={() => onExport('excel')}>
              <IconDownload className="size-4 mr-2" />
              导出
            </Button>
          )}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总互动次数</p>
                <p className="text-2xl font-bold">{stats.totalInteractions}</p>
              </div>
              <IconMessageCircle className="size-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总任务数</p>
                <p className="text-2xl font-bold">{stats.totalTasks}</p>
                <p className="text-xs text-muted-foreground">
                  待处理: {stats.pendingTasks} | 逾期: {stats.overdueTasks}
                </p>
              </div>
              <IconClock className="size-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">完成率</p>
                <p className="text-2xl font-bold">{stats.completionRate.toFixed(1)}%</p>
                <p className="text-xs text-muted-foreground">
                  已完成: {stats.completedTasks}
                </p>
              </div>
              <IconCheckCircle className="size-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">活跃员工</p>
                <p className="text-2xl font-bold">{stats.activeStaff}</p>
                <p className="text-xs text-muted-foreground">
                  平均响应: {stats.avgResponseTime}h
                </p>
              </div>
              <IconUsers className="size-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="interactions">互动分析</TabsTrigger>
          <TabsTrigger value="tasks">任务分析</TabsTrigger>
          <TabsTrigger value="performance">员工表现</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBarChart className="size-5" />
                  活动趋势
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activityData.slice(-7).map((day, index) => (
                    <div key={day.date} className="flex items-center gap-4">
                      <div className="w-16 text-xs text-muted-foreground">
                        {new Date(day.date).toLocaleDateString('zh-CN', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </div>
                      <div className="flex-1 flex items-center gap-2">
                        <div className="flex-1 bg-gray-100 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ 
                              width: `${Math.min((day.interactions / Math.max(...activityData.map(d => d.interactions))) * 100, 100)}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8">{day.interactions}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconTrendingUp className="size-5" />
                  最近活动
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...interactions, ...tasks]
                    .sort((a, b) => new Date(b.createdAt || b.timestamp).getTime() - new Date(a.createdAt || a.timestamp).getTime())
                    .slice(0, 5)
                    .map((item, index) => (
                      <div key={`${item.id}-${index}`} className="flex items-start gap-3 text-sm">
                        <div className="size-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="font-medium">{item.title}</p>
                          <p className="text-muted-foreground text-xs">
                            {formatRelativeTime(new Date(item.createdAt || item.timestamp))}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="interactions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Interaction Types */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconPieChart className="size-5" />
                  互动类型分布
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getInteractionsByType().map((item) => (
                    <div key={item.type} className="flex items-center justify-between">
                      <span className="text-sm">{item.label}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-100 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ 
                              width: `${(item.count / stats.totalInteractions) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Interaction Status */}
            <Card>
              <CardHeader>
                <CardTitle>互动状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {['open', 'in-progress', 'resolved', 'closed'].map(status => {
                    const count = interactions.filter(item => item.status === status).length;
                    const labels = {
                      'open': '开放',
                      'in-progress': '进行中',
                      'resolved': '已解决',
                      'closed': '已关闭',
                    };
                    return (
                      <div key={status} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold">{count}</div>
                        <div className="text-sm text-muted-foreground">{labels[status as keyof typeof labels]}</div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Task Priority Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconAlertTriangle className="size-5" />
                  任务优先级分布
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getTasksByPriority().map((item) => (
                    <div key={item.priority} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={item.color}>
                          {item.label}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-100 rounded-full h-2">
                          <div 
                            className="bg-orange-500 h-2 rounded-full"
                            style={{ 
                              width: `${(item.count / stats.totalTasks) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Task Completion Trends */}
            <Card>
              <CardHeader>
                <CardTitle>任务完成趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activityData.slice(-7).map((day) => (
                    <div key={day.date} className="flex items-center gap-4">
                      <div className="w-16 text-xs text-muted-foreground">
                        {new Date(day.date).toLocaleDateString('zh-CN', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </div>
                      <div className="flex-1 flex items-center gap-2">
                        <div className="flex-1 bg-gray-100 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ 
                              width: `${day.tasks > 0 ? (day.completed / day.tasks) * 100 : 0}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12">
                          {day.completed}/{day.tasks}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <IconUsers className="size-5" />
                员工表现排行
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTopPerformers().map((performer, index) => (
                  <div key={performer.staff.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-center size-8 bg-blue-100 text-blue-600 rounded-full font-bold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">
                        {`${performer.staff.firstName || ''} ${performer.staff.lastName || ''}`.trim() || performer.staff.email}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {performer.staff.role === 'admin' && '管理员'}
                        {performer.staff.role === 'doctor' && '医生'}
                        {performer.staff.role === 'front-desk' && '前台'}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-lg">{performer.completionRate.toFixed(1)}%</div>
                      <div className="text-xs text-muted-foreground">
                        {performer.completed}/{performer.tasks} 任务完成
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
