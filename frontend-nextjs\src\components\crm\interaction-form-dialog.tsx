'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { IconCalendar, IconClock } from '@/components/icons';
import { PatientInteraction, PatientInteractionFormData } from '@/types/clinic';
import { formatDateTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

const interactionSchema = z.object({
  interactionType: z.enum(['phone-call', 'email', 'consultation-note', 'in-person-visit', 'treatment-discussion', 'billing-inquiry']),
  title: z.string().min(1, '请输入互动标题').max(200, '标题不能超过200个字符'),
  notes: z.string().min(1, '请输入详细记录'),
  outcome: z.string().optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['open', 'in-progress', 'resolved', 'closed']).default('open'),
  relatedAppointment: z.string().optional(),
  relatedBill: z.string().optional(),
});

type InteractionFormData = z.infer<typeof interactionSchema>;

interface InteractionFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patientId: string;
  patientName: string;
  interaction?: PatientInteraction;
  defaultType?: string;
  onSubmit: (data: PatientInteractionFormData) => Promise<void>;
  loading?: boolean;
}

const interactionTypeOptions = [
  { value: 'phone-call', label: '电话通话', description: '记录与患者的电话沟通' },
  { value: 'email', label: '邮件沟通', description: '记录邮件往来内容' },
  { value: 'consultation-note', label: '咨询记录', description: '记录咨询或诊疗过程' },
  { value: 'in-person-visit', label: '到院就诊', description: '记录患者到院就诊情况' },
  { value: 'treatment-discussion', label: '治疗讨论', description: '记录治疗方案讨论' },
  { value: 'billing-inquiry', label: '账单咨询', description: '记录账单相关咨询' },
];

const priorityOptions = [
  { value: 'low', label: '低', description: '一般重要性' },
  { value: 'medium', label: '中', description: '中等重要性' },
  { value: 'high', label: '高', description: '高重要性，需要优先处理' },
];

const statusOptions = [
  { value: 'open', label: '开放', description: '新创建的互动记录' },
  { value: 'in-progress', label: '进行中', description: '正在处理中' },
  { value: 'resolved', label: '已解决', description: '问题已解决' },
  { value: 'closed', label: '已关闭', description: '互动已结束' },
];

export function InteractionFormDialog({
  open,
  onOpenChange,
  patientId,
  patientName,
  interaction,
  defaultType,
  onSubmit,
  loading = false,
}: InteractionFormDialogProps) {
  const [followUpDate, setFollowUpDate] = useState<Date | undefined>();
  const [followUpTime, setFollowUpTime] = useState('09:00');

  const isEditing = !!interaction;

  const form = useForm<InteractionFormData>({
    resolver: zodResolver(interactionSchema),
    defaultValues: {
      interactionType: (defaultType as any) || 'phone-call',
      title: '',
      notes: '',
      outcome: '',
      followUpRequired: false,
      followUpDate: '',
      priority: 'medium',
      status: 'open',
      relatedAppointment: '',
      relatedBill: '',
    },
  });

  // Reset form when dialog opens/closes or interaction changes
  useEffect(() => {
    if (open) {
      if (interaction) {
        form.reset({
          interactionType: interaction.interactionType,
          title: interaction.title,
          notes: interaction.notes,
          outcome: interaction.outcome || '',
          followUpRequired: interaction.followUpRequired,
          followUpDate: interaction.followUpDate || '',
          priority: interaction.priority,
          status: interaction.status,
          relatedAppointment: typeof interaction.relatedAppointment === 'string' ? interaction.relatedAppointment : '',
          relatedBill: typeof interaction.relatedBill === 'string' ? interaction.relatedBill : '',
        });
        
        if (interaction.followUpDate) {
          const date = new Date(interaction.followUpDate);
          setFollowUpDate(date);
          setFollowUpTime(date.toTimeString().slice(0, 5));
        }
      } else {
        form.reset({
          interactionType: (defaultType as any) || 'phone-call',
          title: '',
          notes: '',
          outcome: '',
          followUpRequired: false,
          followUpDate: '',
          priority: 'medium',
          status: 'open',
          relatedAppointment: '',
          relatedBill: '',
        });
        setFollowUpDate(undefined);
        setFollowUpTime('09:00');
      }
    }
  }, [open, interaction, defaultType, form]);

  const handleSubmit = async (data: InteractionFormData) => {
    try {
      // Combine follow-up date and time
      if (data.followUpRequired && followUpDate) {
        const [hours, minutes] = followUpTime.split(':');
        const combinedDate = new Date(followUpDate);
        combinedDate.setHours(parseInt(hours), parseInt(minutes));
        data.followUpDate = combinedDate.toISOString();
      }

      const formData: PatientInteractionFormData = {
        ...data,
        patient: patientId,
      };

      await onSubmit(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting interaction:', error);
    }
  };

  const watchFollowUpRequired = form.watch('followUpRequired');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑互动记录' : '添加互动记录'}
          </DialogTitle>
          <DialogDescription>
            为患者 <strong>{patientName}</strong> {isEditing ? '编辑' : '创建'}互动记录
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Interaction Type */}
            <FormField
              control={form.control}
              name="interactionType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>互动类型 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择互动类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {interactionTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>互动标题 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="简要描述此次互动的主题"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    请输入简洁明了的标题，方便后续查找
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>详细记录 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="详细记录互动内容、讨论要点、患者反馈等..."
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    请详细记录互动的具体内容和重要信息
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Outcome */}
            <FormField
              control={form.control}
              name="outcome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>互动结果</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="记录互动的结果、解决方案或后续安排..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    记录此次互动达成的结果或解决方案
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Priority and Status */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>优先级</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {priorityOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>状态</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Follow-up */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="followUpRequired"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>需要跟进</FormLabel>
                      <FormDescription>
                        勾选此项将自动创建跟进任务
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {watchFollowUpRequired && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <FormLabel>跟进日期</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !followUpDate && "text-muted-foreground"
                          )}
                        >
                          <IconCalendar className="mr-2 h-4 w-4" />
                          {followUpDate ? formatDateTime(followUpDate) : "选择日期"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={followUpDate}
                          onSelect={setFollowUpDate}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div>
                    <FormLabel>跟进时间</FormLabel>
                    <div className="relative">
                      <IconClock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        value={followUpTime}
                        onChange={(e) => setFollowUpTime(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : isEditing ? '更新' : '创建'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
