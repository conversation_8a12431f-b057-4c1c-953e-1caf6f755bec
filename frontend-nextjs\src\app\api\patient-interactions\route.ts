import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessR<PERSON>ponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {
  try {
    const payloadClient = createPayloadClient(user);
    const url = new URL(request.url);
    
    // Extract query parameters
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const patientId = url.searchParams.get('patientId');
    const interactionType = url.searchParams.get('interactionType');
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const staffMember = url.searchParams.get('staffMember');
    const search = url.searchParams.get('search');
    
    // Build where clause
    let whereClause: any = {};
    
    if (patientId) {
      whereClause.patient = { equals: patientId };
    }
    
    if (interactionType) {
      whereClause.interactionType = { equals: interactionType };
    }
    
    if (status) {
      whereClause.status = { equals: status };
    }
    
    if (priority) {
      whereClause.priority = { equals: priority };
    }
    
    if (staffMember) {
      whereClause.staffMember = { equals: staffMember };
    }
    
    if (search) {
      whereClause.or = [
        {
          title: {
            contains: search,
          },
        },
        {
          outcome: {
            contains: search,
          },
        },
      ];
    }
    
    // Apply role-based filtering
    if (user.role === 'doctor') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              staffMember: {
                equals: user.id,
              },
            },
            {
              interactionType: {
                in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],
              },
            },
          ],
        },
      ];
    } else if (user.role === 'front-desk') {
      whereClause.and = [
        whereClause,
        {
          interactionType: {
            in: ['phone-call', 'email', 'billing-inquiry'],
          },
        },
      ];
    }
    
    const data = await payloadClient.getPatientInteractions({
      limit,
      page,
      where: whereClause,
      sort: '-timestamp', // Sort by timestamp descending (newest first)
    });
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient interactions:', error);
    return createErrorResponse('Failed to fetch patient interactions');
  }
});

export const POST = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {
  try {
    const payloadClient = createPayloadClient(user);
    const interactionData = await request.json();
    
    // Set the staff member to the current user
    interactionData.staffMember = user.id;
    
    // Validate required fields
    if (!interactionData.patient || !interactionData.interactionType || !interactionData.title || !interactionData.notes) {
      return createErrorResponse('Missing required fields: patient, interactionType, title, notes', 400);
    }
    
    // Validate interaction type based on user role
    if (user.role === 'front-desk') {
      const allowedTypes = ['phone-call', 'email', 'billing-inquiry'];
      if (!allowedTypes.includes(interactionData.interactionType)) {
        return createErrorResponse('Front-desk staff can only create phone-call, email, or billing-inquiry interactions', 403);
      }
    }
    
    const data = await payloadClient.createPatientInteraction(interactionData);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error creating patient interaction:', error);
    return createErrorResponse('Failed to create patient interaction');
  }
});
