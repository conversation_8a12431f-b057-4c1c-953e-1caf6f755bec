{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n\r\n// Date and time formatting utilities\r\nexport function formatDateTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatDate(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n  }).format(date);\r\n}\r\n\r\nexport function formatTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatRelativeTime(date: Date): string {\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) {\r\n    return '刚刚';\r\n  }\r\n\r\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n  if (diffInMinutes < 60) {\r\n    return `${diffInMinutes}分钟前`;\r\n  }\r\n\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  if (diffInHours < 24) {\r\n    return `${diffInHours}小时前`;\r\n  }\r\n\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n  if (diffInDays < 7) {\r\n    return `${diffInDays}天前`;\r\n  }\r\n\r\n  const diffInWeeks = Math.floor(diffInDays / 7);\r\n  if (diffInWeeks < 4) {\r\n    return `${diffInWeeks}周前`;\r\n  }\r\n\r\n  const diffInMonths = Math.floor(diffInDays / 30);\r\n  if (diffInMonths < 12) {\r\n    return `${diffInMonths}个月前`;\r\n  }\r\n\r\n  const diffInYears = Math.floor(diffInDays / 365);\r\n  return `${diffInYears}年前`;\r\n}\r\n\r\n// Task and interaction utilities\r\nexport function isOverdue(dueDate: string | Date): boolean {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  return due < new Date();\r\n}\r\n\r\nexport function getDaysUntilDue(dueDate: string | Date): number {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  const now = new Date();\r\n  const diffInMs = due.getTime() - now.getTime();\r\n  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));\r\n}\r\n\r\nexport function getUrgencyLevel(dueDate: string | Date, priority: string): 'low' | 'medium' | 'high' | 'urgent' {\r\n  const daysUntil = getDaysUntilDue(dueDate);\r\n\r\n  if (daysUntil < 0) return 'urgent'; // Overdue\r\n  if (daysUntil === 0) return 'urgent'; // Due today\r\n  if (daysUntil === 1) return 'high'; // Due tomorrow\r\n\r\n  if (priority === 'urgent') return 'urgent';\r\n  if (priority === 'high') return daysUntil <= 3 ? 'high' : 'medium';\r\n  if (priority === 'medium') return daysUntil <= 7 ? 'medium' : 'low';\r\n\r\n  return 'low';\r\n}\r\n\r\n// Text processing utilities\r\nexport function truncateText(text: string, maxLength: number): string {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength - 3) + '...';\r\n}\r\n\r\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, '<mark>$1</mark>');\r\n}\r\n\r\n// Validation utilities\r\nexport function isValidEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\nexport function isValidPhone(phone: string): boolean {\r\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n  return phoneRegex.test(phone.replace(/\\s/g, ''));\r\n}\r\n\r\n// Array utilities\r\nexport function groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> {\r\n  return array.reduce((groups, item) => {\r\n    const key = keyFn(item);\r\n    if (!groups[key]) {\r\n      groups[key] = [];\r\n    }\r\n    groups[key].push(item);\r\n    return groups;\r\n  }, {} as Record<string, T[]>);\r\n}\r\n\r\nexport function sortBy<T>(array: T[], keyFn: (item: T) => any, direction: 'asc' | 'desc' = 'asc'): T[] {\r\n  return [...array].sort((a, b) => {\r\n    const aVal = keyFn(a);\r\n    const bVal = keyFn(b);\r\n\r\n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\r\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\r\n    return 0;\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ;AAGO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAU;IAC3C,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,GAAG,CAAC;IAC9B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,EAAE,CAAC;IAC3B;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,GAAG,CAAC;IAC7B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,EAAE,CAAC;AAC3B;AAGO,SAAS,UAAU,OAAsB;IAC9C,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,OAAO,MAAM,IAAI;AACnB;AAEO,SAAS,gBAAgB,OAAsB;IACpD,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO;IAC5C,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AAClD;AAEO,SAAS,gBAAgB,OAAsB,EAAE,QAAgB;IACtE,MAAM,YAAY,gBAAgB;IAElC,IAAI,YAAY,GAAG,OAAO,UAAU,UAAU;IAC9C,IAAI,cAAc,GAAG,OAAO,UAAU,YAAY;IAClD,IAAI,cAAc,GAAG,OAAO,QAAQ,eAAe;IAEnD,IAAI,aAAa,UAAU,OAAO;IAClC,IAAI,aAAa,QAAQ,OAAO,aAAa,IAAI,SAAS;IAC1D,IAAI,aAAa,UAAU,OAAO,aAAa,IAAI,WAAW;IAE9D,OAAO;AACT;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,KAAK;AAC5C;AAEO,SAAS,oBAAoB,IAAY,EAAE,UAAkB;IAClE,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,QAAW,KAAU,EAAE,KAA0B;IAC/D,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,MAAM,MAAM;QAClB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,MAAM,CAAC,IAAI,GAAG,EAAE;QAClB;QACA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,KAAuB,EAAE,YAA4B,KAAK;IAC9F,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,MAAM;QACnB,MAAM,OAAO,MAAM;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/not-found.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRouter } from 'next/navigation';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\n\r\nexport default function NotFound() {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <div className='absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center'>\r\n      <span className='from-foreground bg-linear-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent'>\r\n        404\r\n      </span>\r\n      <h2 className='font-heading my-2 text-2xl font-bold'>\r\n        Something&apos;s missing\r\n      </h2>\r\n      <p>\r\n        Sorry, the page you are looking for doesn&apos;t exist or has been\r\n        moved.\r\n      </p>\r\n      <div className='mt-8 flex justify-center gap-2'>\r\n        <Button onClick={() => router.back()} variant='default' size='lg'>\r\n          Go back\r\n        </Button>\r\n        <Button\r\n          onClick={() => router.push('/dashboard')}\r\n          variant='ghost'\r\n          size='lg'\r\n        >\r\n          Back to Home\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAK,WAAU;0BAAuH;;;;;;0BAGvI,4SAAC;gBAAG,WAAU;0BAAuC;;;;;;0BAGrD,4SAAC;0BAAE;;;;;;0BAIH,4SAAC;gBAAI,WAAU;;kCACb,4SAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI;wBAAI,SAAQ;wBAAU,MAAK;kCAAK;;;;;;kCAGlE,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,SAAQ;wBACR,MAAK;kCACN;;;;;;;;;;;;;;;;;;AAMT;GA7BwB;;QACP,oPAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}