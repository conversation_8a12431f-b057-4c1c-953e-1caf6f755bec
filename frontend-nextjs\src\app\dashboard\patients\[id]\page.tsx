import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import FormCardSkeleton from '@/components/form-card-skeleton';
import PatientDetailView from '@/components/patients/patient-detail-view';

export const metadata = {
  title: 'Dashboard : Patient Detail'
};

type PageProps = { 
  params: Promise<{ id: string }> 
};

export default async function PatientDetailPage(props: PageProps) {
  const params = await props.params;
  
  // Validate patient ID
  if (!params.id || params.id === 'new') {
    notFound();
  }

  return (
    <PageContainer scrollable>
      <div className='flex-1 space-y-4'>
        <Suspense fallback={<FormCardSkeleton />}>
          <PatientDetailView patientId={params.id} />
        </Suspense>
      </div>
    </PageContainer>
  );
}
