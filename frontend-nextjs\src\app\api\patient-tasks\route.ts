import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessR<PERSON>ponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {
  try {
    const payloadClient = createPayloadClient(user);
    const url = new URL(request.url);
    
    // Extract query parameters
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const page = parseInt(url.searchParams.get('page') || '1');
    const patientId = url.searchParams.get('patientId');
    const taskType = url.searchParams.get('taskType');
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const assignedTo = url.searchParams.get('assignedTo');
    const createdBy = url.searchParams.get('createdBy');
    const overdue = url.searchParams.get('overdue');
    const search = url.searchParams.get('search');
    
    // Build where clause
    let whereClause: any = {};
    
    if (patientId) {
      whereClause.patient = { equals: patientId };
    }
    
    if (taskType) {
      whereClause.taskType = { equals: taskType };
    }
    
    if (status) {
      whereClause.status = { equals: status };
    }
    
    if (priority) {
      whereClause.priority = { equals: priority };
    }
    
    if (assignedTo) {
      whereClause.assignedTo = { equals: assignedTo };
    }
    
    if (createdBy) {
      whereClause.createdBy = { equals: createdBy };
    }
    
    if (overdue === 'true') {
      whereClause.and = [
        whereClause,
        {
          dueDate: {
            less_than: new Date(),
          },
        },
        {
          status: {
            not_equals: 'completed',
          },
        },
      ];
    }
    
    if (search) {
      whereClause.or = [
        {
          title: {
            contains: search,
          },
        },
        {
          description: {
            contains: search,
          },
        },
      ];
    }
    
    // Apply role-based filtering
    if (user.role === 'doctor') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              assignedTo: {
                equals: user.id,
              },
            },
            {
              createdBy: {
                equals: user.id,
              },
            },
            {
              taskType: {
                in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],
              },
            },
          ],
        },
      ];
    } else if (user.role === 'front-desk') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              assignedTo: {
                equals: user.id,
              },
            },
            {
              taskType: {
                in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],
              },
            },
          ],
        },
      ];
    }
    
    const data = await payloadClient.getPatientTasks({
      limit,
      page,
      where: whereClause,
      sort: '-dueDate', // Sort by due date descending
    });
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient tasks:', error);
    return createErrorResponse('Failed to fetch patient tasks');
  }
});

export const POST = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {
  try {
    const payloadClient = createPayloadClient(user);
    const taskData = await request.json();
    
    // Set the creator to the current user
    taskData.createdBy = user.id;
    
    // If no assignedTo is specified, assign to current user
    if (!taskData.assignedTo) {
      taskData.assignedTo = user.id;
    }
    
    // Validate required fields
    if (!taskData.patient || !taskData.title || !taskData.taskType || !taskData.dueDate) {
      return createErrorResponse('Missing required fields: patient, title, taskType, dueDate', 400);
    }
    
    // Validate task type based on user role
    if (user.role === 'front-desk') {
      const allowedTypes = ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'];
      if (!allowedTypes.includes(taskData.taskType)) {
        return createErrorResponse('Front-desk staff can only create follow-up-call, appointment-scheduling, or billing-follow-up tasks', 403);
      }
    }
    
    const data = await payloadClient.createPatientTask(taskData);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error creating patient task:', error);
    return createErrorResponse('Failed to create patient task');
  }
});
