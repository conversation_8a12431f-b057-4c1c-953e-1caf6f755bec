{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/constants/data.ts"], "sourcesContent": ["import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const navItems: NavItem[] = [\r\n  {\r\n    title: '仪表板',\r\n    url: '/dashboard',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: [], // Empty array as there are no child items for Dashboard\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access dashboard\r\n  },\r\n  {\r\n    title: '预约管理',\r\n    url: '/dashboard/appointments',\r\n    icon: 'calendar',\r\n    shortcut: ['a', 'a'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access appointments\r\n  },\r\n  {\r\n    title: '患者管理',\r\n    url: '/dashboard/patients',\r\n    icon: 'users',\r\n    shortcut: ['p', 'p'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access patients\r\n  },\r\n  {\r\n    title: '治疗项目',\r\n    url: '/dashboard/treatments',\r\n    icon: 'medical',\r\n    shortcut: ['t', 't'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin'] // Only admin can manage treatments\r\n  },\r\n  {\r\n    title: '账单管理',\r\n    url: '/dashboard/billing',\r\n    icon: 'billing',\r\n    shortcut: ['b', 'b'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk'] // Admin and front-desk can manage billing\r\n  },\r\n  {\r\n    title: '系统管理',\r\n    url: '/dashboard/admin',\r\n    icon: 'userPen',\r\n    shortcut: ['u', 'u'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin'] // Only admin can access user management\r\n  },\r\n  {\r\n    title: '账户',\r\n    url: '#', // Placeholder as there is no direct link for the parent\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: '个人资料',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['m', 'm']\r\n      },\r\n      {\r\n        title: '登录',\r\n        shortcut: ['l', 'l'],\r\n        url: '/',\r\n        icon: 'login'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: 'Olivia Martin',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;AAcO,MAAM,WAAsB;IACjC;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAK;SAAI;QACpB,OAAO,EAAE;QACT,OAAO;YAAC;YAAS;YAAc;SAAS,CAAC,iCAAiC;IAC5E;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;YAAC;YAAK;SAAI;QACpB,UAAU;QACV,OAAO,EAAE;QACT,OAAO;YAAC;YAAS;YAAc;SAAS,CAAC,oCAAoC;IAC/E;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;YAAC;YAAK;SAAI;QACpB,UAAU;QACV,OAAO,EAAE;QACT,OAAO;YAAC;YAAS;YAAc;SAAS,CAAC,gCAAgC;IAC3E;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;YAAC;YAAK;SAAI;QACpB,UAAU;QACV,OAAO,EAAE;QACT,OAAO;YAAC;SAAQ,CAAC,mCAAmC;IACtD;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;YAAC;YAAK;SAAI;QACpB,UAAU;QACV,OAAO,EAAE;QACT,OAAO;YAAC;YAAS;SAAa,CAAC,0CAA0C;IAC3E;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;YAAC;YAAK;SAAI;QACpB,UAAU;QACV,OAAO,EAAE;QACT,OAAO;YAAC;SAAQ,CAAC,wCAAwC;IAC3D;IACA;QACE,OAAO;QACP,KAAK;QACL,MAAM;QACN,UAAU;QACV,OAAO;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,UAAU;oBAAC;oBAAK;iBAAI;YACtB;YACA;gBACE,OAAO;gBACP,UAAU;oBAAC;oBAAK;iBAAI;gBACpB,KAAK;gBACL,MAAM;YACR;SACD;IACH;CACD;AAWM,MAAM,kBAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/kbar/result-item.tsx"], "sourcesContent": ["import type { ActionId, ActionImpl } from 'kbar';\r\nimport * as React from 'react';\r\n\r\nconst ResultItem = React.forwardRef(\r\n  (\r\n    {\r\n      action,\r\n      active,\r\n      currentRootActionId\r\n    }: {\r\n      action: ActionImpl;\r\n      active: boolean;\r\n      currentRootActionId: ActionId;\r\n    },\r\n    ref: React.Ref<HTMLDivElement>\r\n  ) => {\r\n    const ancestors = React.useMemo(() => {\r\n      if (!currentRootActionId) return action.ancestors;\r\n      const index = action.ancestors.findIndex(\r\n        (ancestor) => ancestor.id === currentRootActionId\r\n      );\r\n      return action.ancestors.slice(index + 1);\r\n    }, [action.ancestors, currentRootActionId]);\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}\r\n      >\r\n        {active && (\r\n          <div\r\n            id='kbar-result-item'\r\n            className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'\r\n          ></div>\r\n        )}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 &&\r\n                ancestors.map((ancestor) => (\r\n                  <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>\r\n                ))}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && (\r\n              <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? (\r\n          <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => (\r\n              <kbd\r\n                key={sc + i}\r\n                className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'\r\n              >\r\n                {sc}\r\n              </kbd>\r\n            ))}\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nResultItem.displayName = 'KBarResultItem';\r\n\r\nexport default ResultItem;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,2BAAa,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAChC,CACE,EACE,MAAM,EACN,MAAM,EACN,mBAAmB,EAKpB,EACD;IAEA,MAAM,YAAY,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAAE;QAC9B,IAAI,CAAC,qBAAqB,OAAO,OAAO,SAAS;QACjD,MAAM,QAAQ,OAAO,SAAS,CAAC,SAAS,CACtC,CAAC,WAAa,SAAS,EAAE,KAAK;QAEhC,OAAO,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ;IACxC,GAAG;QAAC,OAAO,SAAS;QAAE;KAAoB;IAE1C,qBACE,6VAAC;QACC,KAAK;QACL,WAAW,CAAC,wEAAwE,CAAC;;YAEpF,wBACC,6VAAC;gBACC,IAAG;gBACH,WAAU;;;;;;0BAGd,6VAAC;gBAAI,WAAU;;oBACZ,OAAO,IAAI,IAAI,OAAO,IAAI;kCAC3B,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;;oCACE,UAAU,MAAM,GAAG,KAClB,UAAU,GAAG,CAAC,CAAC,yBACb,6VAAC,oTAAA,CAAA,WAAc;;8DACb,6VAAC;oDAAK,WAAU;8DACb,SAAS,IAAI;;;;;;8DAEhB,6VAAC;oDAAK,WAAU;8DAAO;;;;;;;2CAJJ,SAAS,EAAE;;;;;kDAOpC,6VAAC;kDAAM,OAAO,IAAI;;;;;;;;;;;;4BAEnB,OAAO,QAAQ,kBACd,6VAAC;gCAAK,WAAU;0CACb,OAAO,QAAQ;;;;;;;;;;;;;;;;;;YAKvB,OAAO,QAAQ,EAAE,uBAChB,6VAAC;gBAAI,WAAU;0BACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,kBACxB,6VAAC;wBAEC,WAAU;kCAET;uBAHI,KAAK;;;;;;;;;uBAOd;;;;;;;AAGV;AAGF,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/kbar/render-result.tsx"], "sourcesContent": ["import { KBarResults, useMatches } from 'kbar';\r\nimport ResultItem from './result-item';\r\n\r\nexport default function RenderResults() {\r\n  const { results, rootActionId } = useMatches();\r\n\r\n  return (\r\n    <KBarResults\r\n      items={results}\r\n      onRender={({ item, active }) =>\r\n        typeof item === 'string' ? (\r\n          <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div>\r\n        ) : (\r\n          <ResultItem\r\n            action={item}\r\n            active={active}\r\n            currentRootActionId={rootActionId ?? ''}\r\n          />\r\n        )\r\n      }\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mPAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,6VAAC,mPAAA,CAAA,cAAW;QACV,OAAO;QACP,UAAU,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GACzB,OAAO,SAAS,yBACd,6VAAC;gBAAI,WAAU;0BACZ;;;;;uCAGH,6VAAC,4IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,QAAQ;gBACR,qBAAqB,gBAAgB;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/kbar/use-theme-switching.tsx"], "sourcesContent": ["import { useRegisterActions } from 'kbar';\r\nimport { useTheme } from 'next-themes';\r\n\r\nconst useThemeSwitching = () => {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  const toggleTheme = () => {\r\n    setTheme(theme === 'light' ? 'dark' : 'light');\r\n  };\r\n\r\n  const themeAction = [\r\n    {\r\n      id: 'toggleTheme',\r\n      name: 'Toggle Theme',\r\n      shortcut: ['t', 't'],\r\n      section: 'Theme',\r\n      perform: toggleTheme\r\n    },\r\n    {\r\n      id: 'setLightTheme',\r\n      name: 'Set Light Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('light')\r\n    },\r\n    {\r\n      id: 'setDarkTheme',\r\n      name: 'Set Dark Theme',\r\n      section: 'Theme',\r\n      perform: () => setTheme('dark')\r\n    }\r\n  ];\r\n\r\n  useRegisterActions(themeAction, [theme]);\r\n};\r\n\r\nexport default useThemeSwitching;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,oBAAoB;IACxB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;YACN,UAAU;gBAAC;gBAAK;aAAI;YACpB,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS,IAAM,SAAS;QAC1B;KACD;IAED,CAAA,GAAA,mPAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAAC;KAAM;AACzC;uCAEe", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/kbar/index.tsx"], "sourcesContent": ["'use client';\r\nimport { navItems } from '@/constants/data';\r\nimport {\r\n  KBarAnimator,\r\n  KBarPortal,\r\n  KBarPositioner,\r\n  KBarProvider,\r\n  KBarSearch\r\n} from 'kbar';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useMemo } from 'react';\r\nimport RenderResults from './render-result';\r\nimport useThemeSwitching from './use-theme-switching';\r\n\r\nexport default function KBar({ children }: { children: React.ReactNode }) {\r\n  const router = useRouter();\r\n\r\n  // These action are for the navigation\r\n  const actions = useMemo(() => {\r\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\r\n    const navigateTo = (url: string) => {\r\n      router.push(url);\r\n    };\r\n\r\n    return navItems.flatMap((navItem) => {\r\n      // Only include base action if the navItem has a real URL and is not just a container\r\n      const baseAction =\r\n        navItem.url !== '#'\r\n          ? {\r\n              id: `${navItem.title.toLowerCase()}Action`,\r\n              name: navItem.title,\r\n              shortcut: navItem.shortcut,\r\n              keywords: navItem.title.toLowerCase(),\r\n              section: 'Navigation',\r\n              subtitle: `Go to ${navItem.title}`,\r\n              perform: () => navigateTo(navItem.url)\r\n            }\r\n          : null;\r\n\r\n      // Map child items into actions\r\n      const childActions =\r\n        navItem.items?.map((childItem) => ({\r\n          id: `${childItem.title.toLowerCase()}Action`,\r\n          name: childItem.title,\r\n          shortcut: childItem.shortcut,\r\n          keywords: childItem.title.toLowerCase(),\r\n          section: navItem.title,\r\n          subtitle: `Go to ${childItem.title}`,\r\n          perform: () => navigateTo(childItem.url)\r\n        })) ?? [];\r\n\r\n      // Return only valid actions (ignoring null base actions for containers)\r\n      return baseAction ? [baseAction, ...childActions] : childActions;\r\n    });\r\n  }, [router]);\r\n\r\n  return (\r\n    <KBarProvider actions={actions}>\r\n      <KBarComponent>{children}</KBarComponent>\r\n    </KBarProvider>\r\n  );\r\n}\r\nconst KBarComponent = ({ children }: { children: React.ReactNode }) => {\r\n  useThemeSwitching();\r\n\r\n  return (\r\n    <>\r\n      <KBarPortal>\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm'>\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg'>\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAOA;AACA;AACA;AACA;AAZA;;;;;;;;AAce,SAAS,KAAK,EAAE,QAAQ,EAAiC;IACtE,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IAEvB,sCAAsC;IACtC,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACtB,iFAAiF;QACjF,MAAM,aAAa,CAAC;YAClB,OAAO,IAAI,CAAC;QACd;QAEA,OAAO,wHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,qFAAqF;YACrF,MAAM,aACJ,QAAQ,GAAG,KAAK,MACZ;gBACE,IAAI,GAAG,QAAQ,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;gBAC1C,MAAM,QAAQ,KAAK;gBACnB,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,QAAQ,KAAK,CAAC,WAAW;gBACnC,SAAS;gBACT,UAAU,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBAClC,SAAS,IAAM,WAAW,QAAQ,GAAG;YACvC,IACA;YAEN,+BAA+B;YAC/B,MAAM,eACJ,QAAQ,KAAK,EAAE,IAAI,CAAC,YAAc,CAAC;oBACjC,IAAI,GAAG,UAAU,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;oBAC5C,MAAM,UAAU,KAAK;oBACrB,UAAU,UAAU,QAAQ;oBAC5B,UAAU,UAAU,KAAK,CAAC,WAAW;oBACrC,SAAS,QAAQ,KAAK;oBACtB,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;oBACpC,SAAS,IAAM,WAAW,UAAU,GAAG;gBACzC,CAAC,MAAM,EAAE;YAEX,wEAAwE;YACxE,OAAO,aAAa;gBAAC;mBAAe;aAAa,GAAG;QACtD;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,6VAAC,mPAAA,CAAA,eAAY;QAAC,SAAS;kBACrB,cAAA,6VAAC;sBAAe;;;;;;;;;;;AAGtB;AACA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;IAChE,CAAA,GAAA,uJAAA,CAAA,UAAiB,AAAD;IAEhB,qBACE;;0BACE,6VAAC,mPAAA,CAAA,aAAU;0BACT,cAAA,6VAAC,mPAAA,CAAA,iBAAc;oBAAC,WAAU;8BACxB,cAAA,6VAAC,mPAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,mPAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,8IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;YAKrB;;;AAGP", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/collapsible.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot='collapsible' {...props} />;\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot='collapsible-trigger'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot='collapsible-content'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6VAAC,2QAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6VAAC,2QAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6VAAC,2QAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n\r\n// Date and time formatting utilities\r\nexport function formatDateTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatDate(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n  }).format(date);\r\n}\r\n\r\nexport function formatTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatRelativeTime(date: Date): string {\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) {\r\n    return '刚刚';\r\n  }\r\n\r\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n  if (diffInMinutes < 60) {\r\n    return `${diffInMinutes}分钟前`;\r\n  }\r\n\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  if (diffInHours < 24) {\r\n    return `${diffInHours}小时前`;\r\n  }\r\n\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n  if (diffInDays < 7) {\r\n    return `${diffInDays}天前`;\r\n  }\r\n\r\n  const diffInWeeks = Math.floor(diffInDays / 7);\r\n  if (diffInWeeks < 4) {\r\n    return `${diffInWeeks}周前`;\r\n  }\r\n\r\n  const diffInMonths = Math.floor(diffInDays / 30);\r\n  if (diffInMonths < 12) {\r\n    return `${diffInMonths}个月前`;\r\n  }\r\n\r\n  const diffInYears = Math.floor(diffInDays / 365);\r\n  return `${diffInYears}年前`;\r\n}\r\n\r\n// Task and interaction utilities\r\nexport function isOverdue(dueDate: string | Date): boolean {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  return due < new Date();\r\n}\r\n\r\nexport function getDaysUntilDue(dueDate: string | Date): number {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  const now = new Date();\r\n  const diffInMs = due.getTime() - now.getTime();\r\n  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));\r\n}\r\n\r\nexport function getUrgencyLevel(dueDate: string | Date, priority: string): 'low' | 'medium' | 'high' | 'urgent' {\r\n  const daysUntil = getDaysUntilDue(dueDate);\r\n\r\n  if (daysUntil < 0) return 'urgent'; // Overdue\r\n  if (daysUntil === 0) return 'urgent'; // Due today\r\n  if (daysUntil === 1) return 'high'; // Due tomorrow\r\n\r\n  if (priority === 'urgent') return 'urgent';\r\n  if (priority === 'high') return daysUntil <= 3 ? 'high' : 'medium';\r\n  if (priority === 'medium') return daysUntil <= 7 ? 'medium' : 'low';\r\n\r\n  return 'low';\r\n}\r\n\r\n// Text processing utilities\r\nexport function truncateText(text: string, maxLength: number): string {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength - 3) + '...';\r\n}\r\n\r\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, '<mark>$1</mark>');\r\n}\r\n\r\n// Validation utilities\r\nexport function isValidEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\nexport function isValidPhone(phone: string): boolean {\r\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n  return phoneRegex.test(phone.replace(/\\s/g, ''));\r\n}\r\n\r\n// Array utilities\r\nexport function groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> {\r\n  return array.reduce((groups, item) => {\r\n    const key = keyFn(item);\r\n    if (!groups[key]) {\r\n      groups[key] = [];\r\n    }\r\n    groups[key].push(item);\r\n    return groups;\r\n  }, {} as Record<string, T[]>);\r\n}\r\n\r\nexport function sortBy<T>(array: T[], keyFn: (item: T) => any, direction: 'asc' | 'desc' = 'asc'): T[] {\r\n  return [...array].sort((a, b) => {\r\n    const aVal = keyFn(a);\r\n    const bVal = keyFn(b);\r\n\r\n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\r\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\r\n    return 0;\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ;AAGO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAU;IAC3C,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,GAAG,CAAC;IAC9B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,EAAE,CAAC;IAC3B;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,GAAG,CAAC;IAC7B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,EAAE,CAAC;AAC3B;AAGO,SAAS,UAAU,OAAsB;IAC9C,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,OAAO,MAAM,IAAI;AACnB;AAEO,SAAS,gBAAgB,OAAsB;IACpD,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO;IAC5C,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AAClD;AAEO,SAAS,gBAAgB,OAAsB,EAAE,QAAgB;IACtE,MAAM,YAAY,gBAAgB;IAElC,IAAI,YAAY,GAAG,OAAO,UAAU,UAAU;IAC9C,IAAI,cAAc,GAAG,OAAO,UAAU,YAAY;IAClD,IAAI,cAAc,GAAG,OAAO,QAAQ,eAAe;IAEnD,IAAI,aAAa,UAAU,OAAO;IAClC,IAAI,aAAa,QAAQ,OAAO,aAAa,IAAI,SAAS;IAC1D,IAAI,aAAa,UAAU,OAAO,aAAa,IAAI,WAAW;IAE9D,OAAO;AACT;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,KAAK;AAC5C;AAEO,SAAS,oBAAoB,IAAY,EAAE,UAAkB;IAClE,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,QAAW,KAAU,EAAE,KAA0B;IAC/D,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,MAAM,MAAM;QAClB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,MAAM,CAAC,IAAI,GAAG,EAAE;QAClB;QACA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,KAAuB,EAAE,YAA4B,KAAK;IAC9F,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,MAAM;QACnB,MAAM,OAAO,MAAM;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot='dropdown-menu' {...props} />;\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot='dropdown-menu-portal' {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot='dropdown-menu-trigger'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot='dropdown-menu-content'\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot='dropdown-menu-group' {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = 'default',\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean;\r\n  variant?: 'default' | 'destructive';\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot='dropdown-menu-item'\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot='dropdown-menu-checkbox-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className='size-4' />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot='dropdown-menu-radio-group'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot='dropdown-menu-radio-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className='size-2 fill-current' />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot='dropdown-menu-label'\r\n      data-inset={inset}\r\n      className={cn(\r\n        'px-2 py-1.5 text-sm font-medium data-[inset]:pl-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot='dropdown-menu-separator'\r\n      className={cn('bg-border -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='dropdown-menu-shortcut'\r\n      className={cn(\r\n        'text-muted-foreground ml-auto text-xs tracking-widest',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot='dropdown-menu-sub' {...props} />;\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot='dropdown-menu-sub-trigger'\r\n      data-inset={inset}\r\n      className={cn(\r\n        'focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className='ml-auto size-4' />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot='dropdown-menu-sub-content'\r\n      className={cn(\r\n        'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6VAAC,mRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6VAAC,mRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6VAAC,mRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6VAAC,mRAAA,CAAA,SAA4B;kBAC3B,cAAA,6VAAC,mRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6VAAC,mRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6VAAC,mRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6VAAC,mRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6VAAC;gBAAK,WAAU;0BACd,cAAA,6VAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,6VAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6VAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6VAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6VAAC;gBAAK,WAAU;0BACd,cAAA,6VAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,6VAAC,8RAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6VAAC,mRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6VAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6VAAC,mRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6VAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6VAAC,8SAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6VAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(\r\n    undefined\r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener('change', onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener('change', onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAC3C;IAGF,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot='input'\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6VAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = 'horizontal',\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot='separator-root'\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6VAAC,4QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot='sheet' {...props} />;\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot='sheet-trigger' {...props} />;\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot='sheet-close' {...props} />;\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot='sheet-portal' {...props} />;\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot='sheet-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = 'right',\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: 'top' | 'right' | 'bottom' | 'left';\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot='sheet-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500',\r\n          side === 'right' &&\r\n            'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm',\r\n          side === 'left' &&\r\n            'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm',\r\n          side === 'top' &&\r\n            'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b',\r\n          side === 'bottom' &&\r\n            'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className='ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none'>\r\n          <XIcon className='size-4' />\r\n          <span className='sr-only'>Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  );\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sheet-header'\r\n      className={cn('flex flex-col gap-1.5 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sheet-footer'\r\n      className={cn('mt-auto flex flex-col gap-2 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot='sheet-title'\r\n      className={cn('text-foreground font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot='sheet-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6VAAC,+QAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6VAAC,+QAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6VAAC,+QAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6VAAC,+QAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6VAAC;;0BACC,6VAAC;;;;;0BACD,6VAAC,+QAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6VAAC,+QAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6VAAC,oRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6VAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6VAAC,+QAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='skeleton'\r\n      className={cn('bg-accent animate-pulse rounded-md', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot='tooltip-provider'\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot='tooltip-content'\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6VAAC,6QAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6VAAC;kBACC,cAAA,6VAAC,6QAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6VAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6VAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,6VAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6VAAC,6QAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { VariantProps, cva } from 'class-variance-authority';\r\nimport { PanelLeftIcon } from 'lucide-react';\r\nimport { useIsMobile } from '@/hooks/use-mobile';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle\r\n} from '@/components/ui/sheet';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger\r\n} from '@/components/ui/tooltip';\r\n\r\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = '16rem';\r\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\r\nconst SIDEBAR_WIDTH_ICON = '3rem';\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\r\n\r\ntype SidebarContextProps = {\r\n  state: 'expanded' | 'collapsed';\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error('useSidebar must be used within a SidebarProvider.');\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === 'function' ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    return () => window.removeEventListener('keydown', handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? 'expanded' : 'collapsed';\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot='sidebar-wrapper'\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH,\r\n              '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\r\n              ...style\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            'group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full',\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = 'left',\r\n  variant = 'sidebar',\r\n  collapsible = 'offcanvas',\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  side?: 'left' | 'right';\r\n  variant?: 'sidebar' | 'floating' | 'inset';\r\n  collapsible?: 'offcanvas' | 'icon' | 'none';\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === 'none') {\r\n    return (\r\n      <div\r\n        data-slot='sidebar'\r\n        className={cn(\r\n          'bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar='sidebar'\r\n          data-slot='sidebar'\r\n          data-mobile='true'\r\n          className='bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden'\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH_MOBILE\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className='sr-only'>\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className='flex h-full w-full flex-col'>{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className='group peer text-sidebar-foreground hidden md:block'\r\n      data-state={state}\r\n      data-collapsible={state === 'collapsed' ? collapsible : ''}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot='sidebar'\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot='sidebar-gap'\r\n        className={cn(\r\n          'relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear',\r\n          'group-data-[collapsible=offcanvas]:w-0',\r\n          'group-data-[side=right]:rotate-180',\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)'\r\n        )}\r\n      />\r\n      <div\r\n        data-slot='sidebar-container'\r\n        className={cn(\r\n          'fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex',\r\n          side === 'left'\r\n            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'\r\n            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar='sidebar'\r\n          data-slot='sidebar-inner'\r\n          className='bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm'\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar='trigger'\r\n      data-slot='sidebar-trigger'\r\n      variant='ghost'\r\n      size='icon'\r\n      className={cn('size-7', className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className='sr-only'>Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<'button'>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar='rail'\r\n      data-slot='sidebar-rail'\r\n      aria-label='Toggle Sidebar'\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title='Toggle Sidebar'\r\n      className={cn(\r\n        'hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex',\r\n        'in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize',\r\n        '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize',\r\n        'hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full',\r\n        '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2',\r\n        '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<'main'>) {\r\n  return (\r\n    <main\r\n      data-slot='sidebar-inset'\r\n      className={cn(\r\n        'bg-background relative flex w-full flex-1 flex-col',\r\n        'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot='sidebar-input'\r\n      data-sidebar='input'\r\n      className={cn('bg-background h-8 w-full shadow-none', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-header'\r\n      data-sidebar='header'\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-footer'\r\n      data-sidebar='footer'\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot='sidebar-separator'\r\n      data-sidebar='separator'\r\n      className={cn('bg-sidebar-border mx-2 w-auto', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-content'\r\n      data-sidebar='content'\r\n      className={cn(\r\n        'flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-group'\r\n      data-sidebar='group'\r\n      className={cn('relative flex w-full min-w-0 flex-col p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'div';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-group-label'\r\n      data-sidebar='group-label'\r\n      className={cn(\r\n        'text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-group-action'\r\n      data-sidebar='group-action'\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-group-content'\r\n      data-sidebar='group-content'\r\n      className={cn('w-full text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot='sidebar-menu'\r\n      data-sidebar='menu'\r\n      className={cn('flex w-full min-w-0 flex-col gap-1', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='sidebar-menu-item'\r\n      data-sidebar='menu-item'\r\n      className={cn('group/menu-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\r\n        outline:\r\n          'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]'\r\n      },\r\n      size: {\r\n        default: 'h-8 text-sm',\r\n        sm: 'h-7 text-xs',\r\n        lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = 'default',\r\n  size = 'default',\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : 'button';\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot='sidebar-menu-button'\r\n      data-sidebar='menu-button'\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  if (typeof tooltip === 'string') {\r\n    tooltip = {\r\n      children: tooltip\r\n    };\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side='right'\r\n        align='center'\r\n        hidden={state !== 'collapsed' || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-menu-action'\r\n      data-sidebar='menu-action'\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        showOnHover &&\r\n          'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='sidebar-menu-badge'\r\n      data-sidebar='menu-badge'\r\n      className={cn(\r\n        'text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none',\r\n        'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot='sidebar-menu-skeleton'\r\n      data-sidebar='menu-skeleton'\r\n      className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className='size-4 rounded-md'\r\n          data-sidebar='menu-skeleton-icon'\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className='h-4 max-w-(--skeleton-width) flex-1'\r\n        data-sidebar='menu-skeleton-text'\r\n        style={\r\n          {\r\n            '--skeleton-width': width\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot='sidebar-menu-sub'\r\n      data-sidebar='menu-sub'\r\n      className={cn(\r\n        'border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='sidebar-menu-sub-item'\r\n      data-sidebar='menu-sub-item'\r\n      className={cn('group/menu-sub-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = 'md',\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean;\r\n  size?: 'sm' | 'md';\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='sidebar-menu-sub-button'\r\n      data-sidebar='menu-sub-button'\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground',\r\n        size === 'sm' && 'text-xs',\r\n        size === 'md' && 'text-sm',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAnBA;;;;;;;;;;;;;;AA0BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6VAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6VAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6VAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6VAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6VAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6VAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6VAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6VAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6VAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6VAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6VAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6VAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6VAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6VAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6VAAC,wSAAA,CAAA,gBAAa;;;;;0BACd,6VAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6VAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6VAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6VAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6VAAC,mIAAA,CAAA,UAAO;;0BACN,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6VAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6VAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6VAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot='avatar'\r\n      className={cn(\r\n        'relative flex size-8 shrink-0 overflow-hidden rounded-full',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot='avatar-image'\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot='avatar-fallback'\r\n      className={cn(\r\n        'bg-muted flex size-full items-center justify-center rounded-full',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6VAAC,+QAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/user-avatar-profile.tsx"], "sourcesContent": ["import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\n\r\ninterface UserAvatarProfileProps {\r\n  className?: string;\r\n  showInfo?: boolean;\r\n  user: {\r\n    imageUrl?: string;\r\n    fullName?: string | null;\r\n    emailAddresses: Array<{ emailAddress: string }>;\r\n  } | null;\r\n}\r\n\r\nexport function UserAvatarProfile({\r\n  className,\r\n  showInfo = false,\r\n  user\r\n}: UserAvatarProfileProps) {\r\n  return (\r\n    <div className='flex items-center gap-2'>\r\n      <Avatar className={className}>\r\n        <AvatarImage src={user?.imageUrl || ''} alt={user?.fullName || ''} />\r\n        <AvatarFallback className='rounded-lg'>\r\n          {user?.fullName?.slice(0, 2)?.toUpperCase() || 'CN'}\r\n        </AvatarFallback>\r\n      </Avatar>\r\n\r\n      {showInfo && (\r\n        <div className='grid flex-1 text-left text-sm leading-tight'>\r\n          <span className='truncate font-semibold'>{user?.fullName || ''}</span>\r\n          <span className='truncate text-xs'>\r\n            {user?.emailAddresses[0].emailAddress || ''}\r\n          </span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAYO,SAAS,kBAAkB,EAChC,SAAS,EACT,WAAW,KAAK,EAChB,IAAI,EACmB;IACvB,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC,kIAAA,CAAA,SAAM;gBAAC,WAAW;;kCACjB,6VAAC,kIAAA,CAAA,cAAW;wBAAC,KAAK,MAAM,YAAY;wBAAI,KAAK,MAAM,YAAY;;;;;;kCAC/D,6VAAC,kIAAA,CAAA,iBAAc;wBAAC,WAAU;kCACvB,MAAM,UAAU,MAAM,GAAG,IAAI,iBAAiB;;;;;;;;;;;;YAIlD,0BACC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAK,WAAU;kCAA0B,MAAM,YAAY;;;;;;kCAC5D,6VAAC;wBAAK,WAAU;kCACb,MAAM,cAAc,CAAC,EAAE,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-media-query.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\r\n\r\nexport function useMediaQuery() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const mediaQuery = window.matchMedia('(max-width: 768px)');\r\n    setIsOpen(mediaQuery.matches);\r\n\r\n    const handler = (e: MediaQueryListEvent) => {\r\n      setIsOpen(e.matches);\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handler);\r\n    return () => mediaQuery.removeEventListener('change', handler);\r\n  }, []);\r\n\r\n  return { isOpen };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,UAAU,WAAW,OAAO;QAE5B,MAAM,UAAU,CAAC;YACf,UAAU,EAAE,OAAO;QACrB;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;QAAE;IAAO;AAClB", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/role-utils.ts"], "sourcesContent": ["// Role-based access control utilities for frontend components\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport type UserRole = 'admin' | 'front-desk' | 'doctor';\n\nexport interface RolePermissions {\n  canCreatePatients: boolean;\n  canEditPatients: boolean;\n  canDeletePatients: boolean;\n  canViewMedicalNotes: boolean;\n  canEditMedicalNotes: boolean;\n  canCreateAppointments: boolean;\n  canEditAllAppointments: boolean;\n  canEditOwnAppointments: boolean;\n  canDeleteAppointments: boolean;\n  canCreateTreatments: boolean;\n  canEditTreatments: boolean;\n  canDeleteTreatments: boolean;\n  canManageUsers: boolean;\n  canViewAnalytics: boolean;\n  canViewFinancialData: boolean;\n  // Billing and Payment Permissions\n  canCreateBills: boolean;\n  canEditBills: boolean;\n  canDeleteBills: boolean;\n  canViewAllBills: boolean;\n  canViewOwnBills: boolean;\n  canProcessPayments: boolean;\n  canViewPayments: boolean;\n  canGenerateReceipts: boolean;\n  canHandleRefunds: boolean;\n  canGenerateReports: boolean;\n  canViewDetailedFinancials: boolean;\n  canManagePaymentMethods: boolean;\n  // Advanced Billing Permissions\n  canApplyDiscounts: boolean;\n  canApproveRefunds: boolean;\n  canViewSensitiveFinancials: boolean;\n  canExportFinancialData: boolean;\n  canBulkUpdateBills: boolean;\n  canOverridePaymentLimits: boolean;\n  canAccessAuditLogs: boolean;\n  canManageDeposits: boolean;\n  canProcessDepositRefunds: boolean;\n  canViewPatientFinancialHistory: boolean;\n  canModifyBillDueDates: boolean;\n  canWaiveFees: boolean;\n  canAccessAdvancedReports: boolean;\n  canManageBillingSettings: boolean;\n  // Deposit Management Permissions\n  canCreateDeposits: boolean;\n  canEditDeposits: boolean;\n  canDeleteDeposits: boolean;\n  canApplyDeposits: boolean;\n  canRefundDeposits: boolean;\n  canViewDepositHistory: boolean;\n  // Financial Reporting Permissions\n  canViewDailyReports: boolean;\n  canViewMonthlyReports: boolean;\n  canViewYearlyReports: boolean;\n  canViewOutstandingReports: boolean;\n  canViewPaymentMethodReports: boolean;\n  canViewTreatmentRevenueReports: boolean;\n  canExportReports: boolean;\n  canScheduleReports: boolean;\n}\n\n/**\n * Get permissions for a specific user role\n */\nexport function getRolePermissions(role: UserRole): RolePermissions {\n  switch (role) {\n    case 'admin':\n      return {\n        canCreatePatients: true,\n        canEditPatients: true,\n        canDeletePatients: true,\n        canViewMedicalNotes: true,\n        canEditMedicalNotes: true,\n        canCreateAppointments: true,\n        canEditAllAppointments: true,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: true,\n        canCreateTreatments: true,\n        canEditTreatments: true,\n        canDeleteTreatments: true,\n        canManageUsers: true,\n        canViewAnalytics: true,\n        canViewFinancialData: true,\n        // Billing and Payment Permissions - Admin has full access\n        canCreateBills: true,\n        canEditBills: true,\n        canDeleteBills: true,\n        canViewAllBills: true,\n        canViewOwnBills: true,\n        canProcessPayments: true,\n        canViewPayments: true,\n        canGenerateReceipts: true,\n        canHandleRefunds: true,\n        canGenerateReports: true,\n        canViewDetailedFinancials: true,\n        canManagePaymentMethods: true,\n        // Advanced Billing Permissions - Admin has full access\n        canApplyDiscounts: true,\n        canApproveRefunds: true,\n        canViewSensitiveFinancials: true,\n        canExportFinancialData: true,\n        canBulkUpdateBills: true,\n        canOverridePaymentLimits: true,\n        canAccessAuditLogs: true,\n        canManageDeposits: true,\n        canProcessDepositRefunds: true,\n        canViewPatientFinancialHistory: true,\n        canModifyBillDueDates: true,\n        canWaiveFees: true,\n        canAccessAdvancedReports: true,\n        canManageBillingSettings: true,\n        // Deposit Management Permissions - Admin has full access\n        canCreateDeposits: true,\n        canEditDeposits: true,\n        canDeleteDeposits: true,\n        canApplyDeposits: true,\n        canRefundDeposits: true,\n        canViewDepositHistory: true,\n        // Financial Reporting Permissions - Admin has full access\n        canViewDailyReports: true,\n        canViewMonthlyReports: true,\n        canViewYearlyReports: true,\n        canViewOutstandingReports: true,\n        canViewPaymentMethodReports: true,\n        canViewTreatmentRevenueReports: true,\n        canExportReports: true,\n        canScheduleReports: true,\n      };\n\n    case 'doctor':\n      return {\n        canCreatePatients: false,\n        canEditPatients: false, // Can only edit medical notes\n        canDeletePatients: false,\n        canViewMedicalNotes: true,\n        canEditMedicalNotes: true,\n        canCreateAppointments: false,\n        canEditAllAppointments: false,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: false,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - Doctor has limited access\n        canCreateBills: false,\n        canEditBills: false,\n        canDeleteBills: false,\n        canViewAllBills: false,\n        canViewOwnBills: true, // Can view bills related to their appointments\n        canProcessPayments: false,\n        canViewPayments: true, // Can view payment status for their patients\n        canGenerateReceipts: false,\n        canHandleRefunds: false,\n        canGenerateReports: false,\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: false,\n        // Advanced Billing Permissions - Doctor has very limited access\n        canApplyDiscounts: false,\n        canApproveRefunds: false,\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: false,\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: false,\n        canProcessDepositRefunds: false,\n        canViewPatientFinancialHistory: true, // Can view their patients' financial history\n        canModifyBillDueDates: false,\n        canWaiveFees: false,\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - Doctor has no access\n        canCreateDeposits: false,\n        canEditDeposits: false,\n        canDeleteDeposits: false,\n        canApplyDeposits: false,\n        canRefundDeposits: false,\n        canViewDepositHistory: false,\n        // Financial Reporting Permissions - Doctor has no access\n        canViewDailyReports: false,\n        canViewMonthlyReports: false,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: false,\n        canViewPaymentMethodReports: false,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n\n    case 'front-desk':\n      return {\n        canCreatePatients: true,\n        canEditPatients: true,\n        canDeletePatients: true,\n        canViewMedicalNotes: false,\n        canEditMedicalNotes: false,\n        canCreateAppointments: true,\n        canEditAllAppointments: true,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: true,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - Front-desk handles payments but limited financial access\n        canCreateBills: true,\n        canEditBills: true,\n        canDeleteBills: false, // Cannot delete bills\n        canViewAllBills: true,\n        canViewOwnBills: true,\n        canProcessPayments: true,\n        canViewPayments: true,\n        canGenerateReceipts: true,\n        canHandleRefunds: false, // Cannot handle refunds without approval\n        canGenerateReports: false, // Limited reporting access\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: true,\n        // Advanced Billing Permissions - Front-desk has moderate access\n        canApplyDiscounts: true, // Can apply standard discounts\n        canApproveRefunds: false, // Cannot approve refunds\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: true, // Can perform bulk operations\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: true,\n        canProcessDepositRefunds: false, // Cannot process deposit refunds\n        canViewPatientFinancialHistory: true,\n        canModifyBillDueDates: true, // Can modify due dates\n        canWaiveFees: false, // Cannot waive fees\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - Front-desk has good access\n        canCreateDeposits: true,\n        canEditDeposits: true,\n        canDeleteDeposits: false, // Cannot delete deposits\n        canApplyDeposits: true,\n        canRefundDeposits: false, // Cannot refund deposits\n        canViewDepositHistory: true,\n        // Financial Reporting Permissions - Front-desk has basic reporting access\n        canViewDailyReports: true,\n        canViewMonthlyReports: true,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: true,\n        canViewPaymentMethodReports: true,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n\n    default:\n      // Default to no permissions for unknown roles\n      return {\n        canCreatePatients: false,\n        canEditPatients: false,\n        canDeletePatients: false,\n        canViewMedicalNotes: false,\n        canEditMedicalNotes: false,\n        canCreateAppointments: false,\n        canEditAllAppointments: false,\n        canEditOwnAppointments: false,\n        canDeleteAppointments: false,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - No access for unknown roles\n        canCreateBills: false,\n        canEditBills: false,\n        canDeleteBills: false,\n        canViewAllBills: false,\n        canViewOwnBills: false,\n        canProcessPayments: false,\n        canViewPayments: false,\n        canGenerateReceipts: false,\n        canHandleRefunds: false,\n        canGenerateReports: false,\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: false,\n        // Advanced Billing Permissions - No access for unknown roles\n        canApplyDiscounts: false,\n        canApproveRefunds: false,\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: false,\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: false,\n        canProcessDepositRefunds: false,\n        canViewPatientFinancialHistory: false,\n        canModifyBillDueDates: false,\n        canWaiveFees: false,\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - No access for unknown roles\n        canCreateDeposits: false,\n        canEditDeposits: false,\n        canDeleteDeposits: false,\n        canApplyDeposits: false,\n        canRefundDeposits: false,\n        canViewDepositHistory: false,\n        // Financial Reporting Permissions - No access for unknown roles\n        canViewDailyReports: false,\n        canViewMonthlyReports: false,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: false,\n        canViewPaymentMethodReports: false,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n  }\n}\n\n/**\n * Check if user has a specific permission\n */\nexport function hasPermission(\n  user: AuthenticatedUser | null,\n  permission: keyof RolePermissions\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const permissions = getRolePermissions(user.role);\n  return permissions[permission];\n}\n\n/**\n * Check if user has any of the specified roles\n */\nexport function hasRole(\n  user: AuthenticatedUser | null,\n  roles: UserRole | UserRole[]\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const allowedRoles = Array.isArray(roles) ? roles : [roles];\n  return allowedRoles.includes(user.role);\n}\n\n/**\n * Check if user can access a specific appointment (for doctors)\n */\nexport function canAccessAppointment(\n  user: AuthenticatedUser | null,\n  appointmentPractitionerId: string\n): boolean {\n  if (!user || !user.role) return false;\n  \n  // Admin and front-desk can access all appointments\n  if (user.role === 'admin' || user.role === 'front-desk') {\n    return true;\n  }\n  \n  // Doctors can only access their own appointments\n  if (user.role === 'doctor') {\n    return user.payloadUserId === appointmentPractitionerId;\n  }\n  \n  return false;\n}\n\n/**\n * Get user display name\n */\nexport function getUserDisplayName(user: AuthenticatedUser | null): string {\n  if (!user) return '未知用户';\n\n  if (user.firstName && user.lastName) {\n    return `${user.firstName} ${user.lastName}`;\n  }\n\n  if (user.firstName) {\n    return user.firstName;\n  }\n\n  return user.email;\n}\n\n/**\n * Get role display name\n */\nexport function getRoleDisplayName(role: UserRole): string {\n  switch (role) {\n    case 'admin':\n      return '管理员';\n    case 'front-desk':\n      return '前台';\n    case 'doctor':\n      return '医生';\n    default:\n      return '未知角色';\n  }\n}\n\n/**\n * Get role badge color for UI display\n */\nexport function getRoleBadgeColor(role: UserRole): string {\n  switch (role) {\n    case 'admin':\n      return 'bg-red-100 text-red-800';\n    case 'doctor':\n      return 'bg-blue-100 text-blue-800';\n    case 'front-desk':\n      return 'bg-green-100 text-green-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n}\n\n/**\n * Navigation items that should be hidden based on role\n */\nexport function getHiddenNavItems(role: UserRole): string[] {\n  switch (role) {\n    case 'admin':\n      return []; // Admin can see everything\n    \n    case 'doctor':\n      return ['treatments', 'analytics', 'users']; // Hide treatment management, analytics, user management\n    \n    case 'front-desk':\n      return ['analytics', 'users']; // Hide analytics and user management\n    \n    default:\n      return ['appointments', 'patients', 'treatments', 'analytics', 'users']; // Hide everything for unknown roles\n  }\n}\n\n/**\n * Check if a navigation item should be visible for the user\n */\nexport function isNavItemVisible(\n  user: AuthenticatedUser | null,\n  navItem: string\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const hiddenItems = getHiddenNavItems(user.role);\n  return !hiddenItems.includes(navItem);\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;;;;;;;;;AAsEvD,SAAS,mBAAmB,IAAc;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,qBAAqB;gBACrB,qBAAqB;gBACrB,uBAAuB;gBACvB,wBAAwB;gBACxB,wBAAwB;gBACxB,uBAAuB;gBACvB,qBAAqB;gBACrB,mBAAmB;gBACnB,qBAAqB;gBACrB,gBAAgB;gBAChB,kBAAkB;gBAClB,sBAAsB;gBACtB,0DAA0D;gBAC1D,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,iBAAiB;gBACjB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;gBACpB,2BAA2B;gBAC3B,yBAAyB;gBACzB,uDAAuD;gBACvD,mBAAmB;gBACnB,mBAAmB;gBACnB,4BAA4B;gBAC5B,wBAAwB;gBACxB,oBAAoB;gBACpB,0BAA0B;gBAC1B,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B;gBAC1B,gCAAgC;gBAChC,uBAAuB;gBACvB,cAAc;gBACd,0BAA0B;gBAC1B,0BAA0B;gBAC1B,yDAAyD;gBACzD,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,mBAAmB;gBACnB,uBAAuB;gBACvB,0DAA0D;gBAC1D,qBAAqB;gBACrB,uBAAuB;gBACvB,sBAAsB;gBACtB,2BAA2B;gBAC3B,6BAA6B;gBAC7B,gCAAgC;gBAChC,kBAAkB;gBAClB,oBAAoB;YACtB;QAEF,KAAK;YACH,OAAO;gBACL,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,qBAAqB;gBACrB,qBAAqB;gBACrB,uBAAuB;gBACvB,wBAAwB;gBACxB,wBAAwB;gBACxB,uBAAuB;gBACvB,qBAAqB;gBACrB,mBAAmB;gBACnB,qBAAqB;gBACrB,gBAAgB;gBAChB,kBAAkB;gBAClB,sBAAsB;gBACtB,8DAA8D;gBAC9D,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,iBAAiB;gBACjB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;gBACpB,2BAA2B;gBAC3B,yBAAyB;gBACzB,gEAAgE;gBAChE,mBAAmB;gBACnB,mBAAmB;gBACnB,4BAA4B;gBAC5B,wBAAwB;gBACxB,oBAAoB;gBACpB,0BAA0B;gBAC1B,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B;gBAC1B,gCAAgC;gBAChC,uBAAuB;gBACvB,cAAc;gBACd,0BAA0B;gBAC1B,0BAA0B;gBAC1B,wDAAwD;gBACxD,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,mBAAmB;gBACnB,uBAAuB;gBACvB,yDAAyD;gBACzD,qBAAqB;gBACrB,uBAAuB;gBACvB,sBAAsB;gBACtB,2BAA2B;gBAC3B,6BAA6B;gBAC7B,gCAAgC;gBAChC,kBAAkB;gBAClB,oBAAoB;YACtB;QAEF,KAAK;YACH,OAAO;gBACL,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,qBAAqB;gBACrB,qBAAqB;gBACrB,uBAAuB;gBACvB,wBAAwB;gBACxB,wBAAwB;gBACxB,uBAAuB;gBACvB,qBAAqB;gBACrB,mBAAmB;gBACnB,qBAAqB;gBACrB,gBAAgB;gBAChB,kBAAkB;gBAClB,sBAAsB;gBACtB,6FAA6F;gBAC7F,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,iBAAiB;gBACjB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;gBACpB,2BAA2B;gBAC3B,yBAAyB;gBACzB,gEAAgE;gBAChE,mBAAmB;gBACnB,mBAAmB;gBACnB,4BAA4B;gBAC5B,wBAAwB;gBACxB,oBAAoB;gBACpB,0BAA0B;gBAC1B,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B;gBAC1B,gCAAgC;gBAChC,uBAAuB;gBACvB,cAAc;gBACd,0BAA0B;gBAC1B,0BAA0B;gBAC1B,8DAA8D;gBAC9D,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,mBAAmB;gBACnB,uBAAuB;gBACvB,0EAA0E;gBAC1E,qBAAqB;gBACrB,uBAAuB;gBACvB,sBAAsB;gBACtB,2BAA2B;gBAC3B,6BAA6B;gBAC7B,gCAAgC;gBAChC,kBAAkB;gBAClB,oBAAoB;YACtB;QAEF;YACE,8CAA8C;YAC9C,OAAO;gBACL,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,qBAAqB;gBACrB,qBAAqB;gBACrB,uBAAuB;gBACvB,wBAAwB;gBACxB,wBAAwB;gBACxB,uBAAuB;gBACvB,qBAAqB;gBACrB,mBAAmB;gBACnB,qBAAqB;gBACrB,gBAAgB;gBAChB,kBAAkB;gBAClB,sBAAsB;gBACtB,gEAAgE;gBAChE,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,iBAAiB;gBACjB,qBAAqB;gBACrB,kBAAkB;gBAClB,oBAAoB;gBACpB,2BAA2B;gBAC3B,yBAAyB;gBACzB,6DAA6D;gBAC7D,mBAAmB;gBACnB,mBAAmB;gBACnB,4BAA4B;gBAC5B,wBAAwB;gBACxB,oBAAoB;gBACpB,0BAA0B;gBAC1B,oBAAoB;gBACpB,mBAAmB;gBACnB,0BAA0B;gBAC1B,gCAAgC;gBAChC,uBAAuB;gBACvB,cAAc;gBACd,0BAA0B;gBAC1B,0BAA0B;gBAC1B,+DAA+D;gBAC/D,mBAAmB;gBACnB,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,mBAAmB;gBACnB,uBAAuB;gBACvB,gEAAgE;gBAChE,qBAAqB;gBACrB,uBAAuB;gBACvB,sBAAsB;gBACtB,2BAA2B;gBAC3B,6BAA6B;gBAC7B,gCAAgC;gBAChC,kBAAkB;gBAClB,oBAAoB;YACtB;IACJ;AACF;AAKO,SAAS,cACd,IAA8B,EAC9B,UAAiC;IAEjC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,MAAM,cAAc,mBAAmB,KAAK,IAAI;IAChD,OAAO,WAAW,CAAC,WAAW;AAChC;AAKO,SAAS,QACd,IAA8B,EAC9B,KAA4B;IAE5B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,MAAM,eAAe,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IAC3D,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxC;AAKO,SAAS,qBACd,IAA8B,EAC9B,yBAAiC;IAEjC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,mDAAmD;IACnD,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,cAAc;QACvD,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,KAAK,IAAI,KAAK,UAAU;QAC1B,OAAO,KAAK,aAAa,KAAK;IAChC;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,IAA8B;IAC/D,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;QACnC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;IAC7C;IAEA,IAAI,KAAK,SAAS,EAAE;QAClB,OAAO,KAAK,SAAS;IACvB;IAEA,OAAO,KAAK,KAAK;AACnB;AAKO,SAAS,mBAAmB,IAAc;IAC/C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAc;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAc;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO,EAAE,EAAE,2BAA2B;QAExC,KAAK;YACH,OAAO;gBAAC;gBAAc;gBAAa;aAAQ,EAAE,wDAAwD;QAEvG,KAAK;YACH,OAAO;gBAAC;gBAAa;aAAQ,EAAE,qCAAqC;QAEtE;YACE,OAAO;gBAAC;gBAAgB;gBAAY;gBAAc;gBAAa;aAAQ,EAAE,oCAAoC;IACjH;AACF;AAKO,SAAS,iBACd,IAA8B,EAC9B,OAAe;IAEf,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,OAAO;IAEhC,MAAM,cAAc,kBAAkB,KAAK,IAAI;IAC/C,OAAO,CAAC,YAAY,QAAQ,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/role-context.tsx"], "sourcesContent": ["'use client';\n\n// Role-based access control context and hooks\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useUser } from '@clerk/nextjs';\nimport { AuthenticatedUser } from './auth-middleware';\nimport { UserRole, hasPermission, hasRole, getRolePermissions, RolePermissions } from './role-utils';\n\ninterface RoleContextType {\n  user: AuthenticatedUser | null;\n  loading: boolean;\n  error: string | null;\n  permissions: RolePermissions | null;\n  hasPermission: (permission: keyof RolePermissions) => boolean;\n  hasRole: (roles: UserRole | UserRole[]) => boolean;\n  refreshUser: () => Promise<void>;\n}\n\nconst RoleContext = createContext<RoleContextType | undefined>(undefined);\n\ninterface RoleProviderProps {\n  children: React.ReactNode;\n}\n\n/**\n * Role Provider Component\n * Fetches user role information and provides role-based utilities\n */\nexport function RoleProvider({ children }: RoleProviderProps) {\n  const { user: clerkUser, isLoaded: clerkLoaded } = useUser();\n  const [user, setUser] = useState<AuthenticatedUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchUserRole = async () => {\n    if (!clerkUser) {\n      setUser(null);\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Call the auth sync endpoint to get user role from backend\n      const response = await fetch('/api/auth/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to sync user data');\n      }\n\n      const data = await response.json();\n\n      // Use the synced user data from backend\n      const authenticatedUser: AuthenticatedUser = {\n        clerkId: clerkUser.id,\n        email: clerkUser.emailAddresses[0]?.emailAddress || '',\n        firstName: clerkUser.firstName || undefined,\n        lastName: clerkUser.lastName || undefined,\n        role: data.user?.role || 'front-desk', // Use role from backend or default\n        payloadUserId: data.user?.payloadUserId, // Set from backend response\n      };\n\n      setUser(authenticatedUser);\n    } catch (err) {\n      console.error('Error fetching user role:', err);\n      setError(err instanceof Error ? err.message : 'Failed to fetch user role');\n      \n      // Set user with default role on error\n      if (clerkUser) {\n        setUser({\n          clerkId: clerkUser.id,\n          email: clerkUser.emailAddresses[0]?.emailAddress || '',\n          firstName: clerkUser.firstName || undefined,\n          lastName: clerkUser.lastName || undefined,\n          role: 'front-desk', // Default fallback role\n          payloadUserId: undefined,\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (clerkLoaded) {\n      fetchUserRole();\n    }\n  }, [clerkUser?.id, clerkLoaded]); // Use clerkUser.id instead of the whole object\n\n  const permissions = user?.role ? getRolePermissions(user.role) : null;\n\n  const contextValue: RoleContextType = {\n    user,\n    loading,\n    error,\n    permissions,\n    hasPermission: (permission: keyof RolePermissions) => hasPermission(user, permission),\n    hasRole: (roles: UserRole | UserRole[]) => hasRole(user, roles),\n    refreshUser: fetchUserRole,\n  };\n\n  return (\n    <RoleContext.Provider value={contextValue}>\n      {children}\n    </RoleContext.Provider>\n  );\n}\n\n/**\n * Hook to access role context\n */\nexport function useRole(): RoleContextType {\n  const context = useContext(RoleContext);\n  if (context === undefined) {\n    throw new Error('useRole must be used within a RoleProvider');\n  }\n  return context;\n}\n\n/**\n * Hook for permission checking\n */\nexport function usePermission(permission: keyof RolePermissions): boolean {\n  const { hasPermission } = useRole();\n  return hasPermission(permission);\n}\n\n/**\n * Hook for role checking\n */\nexport function useHasRole(roles: UserRole | UserRole[]): boolean {\n  const { hasRole } = useRole();\n  return hasRole(roles);\n}\n\n/**\n * Component wrapper for conditional rendering based on permissions\n */\ninterface PermissionGateProps {\n  permission: keyof RolePermissions;\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nexport function PermissionGate({ permission, children, fallback = null }: PermissionGateProps) {\n  const hasRequiredPermission = usePermission(permission);\n  \n  return hasRequiredPermission ? <>{children}</> : <>{fallback}</>;\n}\n\n/**\n * Component wrapper for conditional rendering based on roles\n */\ninterface RoleGateProps {\n  roles: UserRole | UserRole[];\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nexport function RoleGate({ roles, children, fallback = null }: RoleGateProps) {\n  const hasRequiredRole = useHasRole(roles);\n  \n  return hasRequiredRole ? <>{children}</> : <>{fallback}</>;\n}\n\n/**\n * Higher-order component for role-based access control\n */\nexport function withRoleAccess<P extends object>(\n  Component: React.ComponentType<P>,\n  requiredRoles: UserRole | UserRole[],\n  fallbackComponent?: React.ComponentType<P>\n) {\n  return function RoleProtectedComponent(props: P) {\n    const hasRequiredRole = useHasRole(requiredRoles);\n    \n    if (!hasRequiredRole) {\n      if (fallbackComponent) {\n        const FallbackComponent = fallbackComponent;\n        return <FallbackComponent {...props} />;\n      }\n      return (\n        <div className=\"p-4 text-center text-muted-foreground\">\n          <p>You don't have permission to access this feature.</p>\n        </div>\n      );\n    }\n    \n    return <Component {...props} />;\n  };\n}\n\n/**\n * Loading component for role context\n */\nexport function RoleLoading() {\n  return (\n    <div className=\"flex items-center justify-center p-4\">\n      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n      <span className=\"ml-2 text-muted-foreground\">Loading user permissions...</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA,8CAA8C;AAC9C;AACA;AAEA;AANA;;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,gBAAa,AAAD,EAA+B;AAUxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,EAAE,MAAM,SAAS,EAAE,UAAU,WAAW,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA4B;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW;YACd,QAAQ;YACR,WAAW;YACX;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,wCAAwC;YACxC,MAAM,oBAAuC;gBAC3C,SAAS,UAAU,EAAE;gBACrB,OAAO,UAAU,cAAc,CAAC,EAAE,EAAE,gBAAgB;gBACpD,WAAW,UAAU,SAAS,IAAI;gBAClC,UAAU,UAAU,QAAQ,IAAI;gBAChC,MAAM,KAAK,IAAI,EAAE,QAAQ;gBACzB,eAAe,KAAK,IAAI,EAAE;YAC5B;YAEA,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAE9C,sCAAsC;YACtC,IAAI,WAAW;gBACb,QAAQ;oBACN,SAAS,UAAU,EAAE;oBACrB,OAAO,UAAU,cAAc,CAAC,EAAE,EAAE,gBAAgB;oBACpD,WAAW,UAAU,SAAS,IAAI;oBAClC,UAAU,UAAU,QAAQ,IAAI;oBAChC,MAAM;oBACN,eAAe;gBACjB;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf;QACF;IACF,GAAG;QAAC,WAAW;QAAI;KAAY,GAAG,+CAA+C;IAEjF,MAAM,cAAc,MAAM,OAAO,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,IAAI;IAEjE,MAAM,eAAgC;QACpC;QACA;QACA;QACA;QACA,eAAe,CAAC,aAAsC,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;QAC1E,SAAS,CAAC,QAAiC,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD,EAAE,MAAM;QACzD,aAAa;IACf;IAEA,qBACE,6VAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAKO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oTAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAKO,SAAS,cAAc,UAAiC;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG;IAC1B,OAAO,cAAc;AACvB;AAKO,SAAS,WAAW,KAA4B;IACrD,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,OAAO,QAAQ;AACjB;AAWO,SAAS,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAuB;IAC3F,MAAM,wBAAwB,cAAc;IAE5C,OAAO,sCAAwB;kBAAG;sCAAe;kBAAG;;AACtD;AAWO,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAiB;IAC1E,MAAM,kBAAkB,WAAW;IAEnC,OAAO,gCAAkB;kBAAG;sCAAe;kBAAG;;AAChD;AAKO,SAAS,eACd,SAAiC,EACjC,aAAoC,EACpC,iBAA0C;IAE1C,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,MAAM,kBAAkB,WAAW;QAEnC,IAAI,CAAC,iBAAiB;YACpB,IAAI,mBAAmB;gBACrB,MAAM,oBAAoB;gBAC1B,qBAAO,6VAAC;oBAAmB,GAAG,KAAK;;;;;;YACrC;YACA,qBACE,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;8BAAE;;;;;;;;;;;QAGT;QAEA,qBAAO,6VAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF;AAKO,SAAS;IACd,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;;;;;0BACf,6VAAC;gBAAK,WAAU;0BAA6B;;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 2775, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/icons.tsx"], "sourcesContent": ["import {\r\n  IconAlertTriangle,\r\n  IconArrowRight,\r\n  IconCalendar,\r\n  IconCheck,\r\n  IconChevronLeft,\r\n  IconChevronRight,\r\n  IconCommand,\r\n  IconCreditCard,\r\n  IconFile,\r\n  IconFileText,\r\n  IconHelpCircle,\r\n  IconPhoto,\r\n  IconDeviceLaptop,\r\n  IconLayoutDashboard,\r\n  IconLoader2,\r\n  IconLogin,\r\n  IconProps,\r\n  IconShoppingBag,\r\n  IconMoon,\r\n  IconDotsVertical,\r\n  IconPizza,\r\n  IconPlus,\r\n  IconSettings,\r\n  IconStethoscope,\r\n  IconSun,\r\n  IconTrash,\r\n  IconBrandTwitter,\r\n  IconUser,\r\n  IconUserCircle,\r\n  IconUserEdit,\r\n  IconUsers,\r\n  IconUserX,\r\n  IconX,\r\n  IconLayoutKanban,\r\n  IconBrandGithub\r\n} from '@tabler/icons-react';\r\n\r\nexport type Icon = React.ComponentType<IconProps>;\r\n\r\nexport const Icons = {\r\n  dashboard: IconLayoutDashboard,\r\n  logo: IconCommand,\r\n  login: IconLogin,\r\n  close: IconX,\r\n  product: IconShoppingBag,\r\n  spinner: IconLoader2,\r\n  kanban: IconLayoutKanban,\r\n  chevronLeft: IconChevronLeft,\r\n  chevronRight: IconChevronRight,\r\n  trash: IconTrash,\r\n  employee: IconUserX,\r\n  post: IconFileText,\r\n  page: IconFile,\r\n  userPen: IconUserEdit,\r\n  user2: IconUserCircle,\r\n  media: IconPhoto,\r\n  settings: IconSettings,\r\n  billing: IconCreditCard,\r\n  ellipsis: IconDotsVertical,\r\n  add: IconPlus,\r\n  warning: IconAlertTriangle,\r\n  user: IconUser,\r\n  arrowRight: IconArrowRight,\r\n  help: IconHelpCircle,\r\n  pizza: IconPizza,\r\n  sun: IconSun,\r\n  moon: IconMoon,\r\n  laptop: IconDeviceLaptop,\r\n  github: IconBrandGithub,\r\n  twitter: IconBrandTwitter,\r\n  check: IconCheck,\r\n  // Medical clinic specific icons\r\n  calendar: IconCalendar,\r\n  users: IconUsers,\r\n  medical: IconStethoscope\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAwCO,MAAM,QAAQ;IACnB,WAAW,0UAAA,CAAA,sBAAmB;IAC9B,MAAM,0TAAA,CAAA,cAAW;IACjB,OAAO,sTAAA,CAAA,YAAS;IAChB,OAAO,8SAAA,CAAA,QAAK;IACZ,SAAS,kUAAA,CAAA,kBAAe;IACxB,SAAS,0TAAA,CAAA,cAAW;IACpB,QAAQ,oUAAA,CAAA,mBAAgB;IACxB,aAAa,kUAAA,CAAA,kBAAe;IAC5B,cAAc,oUAAA,CAAA,mBAAgB;IAC9B,OAAO,sTAAA,CAAA,YAAS;IAChB,UAAU,sTAAA,CAAA,YAAS;IACnB,MAAM,4TAAA,CAAA,eAAY;IAClB,MAAM,oTAAA,CAAA,WAAQ;IACd,SAAS,4TAAA,CAAA,eAAY;IACrB,OAAO,gUAAA,CAAA,iBAAc;IACrB,OAAO,sTAAA,CAAA,YAAS;IAChB,UAAU,4TAAA,CAAA,eAAY;IACtB,SAAS,gUAAA,CAAA,iBAAc;IACvB,UAAU,oUAAA,CAAA,mBAAgB;IAC1B,KAAK,oTAAA,CAAA,WAAQ;IACb,SAAS,sUAAA,CAAA,oBAAiB;IAC1B,MAAM,oTAAA,CAAA,WAAQ;IACd,YAAY,gUAAA,CAAA,iBAAc;IAC1B,MAAM,gUAAA,CAAA,iBAAc;IACpB,OAAO,sTAAA,CAAA,YAAS;IAChB,KAAK,kTAAA,CAAA,UAAO;IACZ,MAAM,oTAAA,CAAA,WAAQ;IACd,QAAQ,oUAAA,CAAA,mBAAgB;IACxB,QAAQ,kUAAA,CAAA,kBAAe;IACvB,SAAS,oUAAA,CAAA,mBAAgB;IACzB,OAAO,sTAAA,CAAA,YAAS;IAChB,gCAAgC;IAChC,UAAU,4TAAA,CAAA,eAAY;IACtB,OAAO,sTAAA,CAAA,YAAS;IAChB,SAAS,kUAAA,CAAA,kBAAe;AAC1B", "debugId": null}}, {"offset": {"line": 2856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/org-switcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Check, ChevronsUpDown, GalleryVerticalEnd } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem\r\n} from '@/components/ui/sidebar';\r\n\r\ninterface Tenant {\r\n  id: string;\r\n  name: string;\r\n}\r\n\r\nexport function OrgSwitcher({\r\n  tenants,\r\n  defaultTenant,\r\n  onTenantSwitch\r\n}: {\r\n  tenants: Tenant[];\r\n  defaultTenant: Tenant;\r\n  onTenantSwitch?: (tenantId: string) => void;\r\n}) {\r\n  const [selectedTenant, setSelectedTenant] = React.useState<\r\n    Tenant | undefined\r\n  >(defaultTenant || (tenants.length > 0 ? tenants[0] : undefined));\r\n\r\n  const handleTenantSwitch = (tenant: Tenant) => {\r\n    setSelectedTenant(tenant);\r\n    if (onTenantSwitch) {\r\n      onTenantSwitch(tenant.id);\r\n    }\r\n  };\r\n\r\n  if (!selectedTenant) {\r\n    return null;\r\n  }\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size='lg'\r\n              className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'\r\n            >\r\n              <div className='bg-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg'>\r\n                <GalleryVerticalEnd className='size-4' />\r\n              </div>\r\n              <div className='flex flex-col gap-0.5 leading-none'>\r\n                <span className='font-semibold'>Next Starter</span>\r\n                <span className=''>{selectedTenant.name}</span>\r\n              </div>\r\n              <ChevronsUpDown className='ml-auto' />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className='w-[--radix-dropdown-menu-trigger-width]'\r\n            align='start'\r\n          >\r\n            {tenants.map((tenant) => (\r\n              <DropdownMenuItem\r\n                key={tenant.id}\r\n                onSelect={() => handleTenantSwitch(tenant)}\r\n              >\r\n                {tenant.name}{' '}\r\n                {tenant.id === selectedTenant.id && (\r\n                  <Check className='ml-auto' />\r\n                )}\r\n              </DropdownMenuItem>\r\n            ))}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAEA;AAMA;AAXA;;;;;;AAsBO,SAAS,YAAY,EAC1B,OAAO,EACP,aAAa,EACb,cAAc,EAKf;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAEvD,iBAAiB,CAAC,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG,SAAS;IAE/D,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,IAAI,gBAAgB;YAClB,eAAe,OAAO,EAAE;QAC1B;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IACA,qBACE,6VAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,6VAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,6VAAC,4IAAA,CAAA,eAAY;;kCACX,6VAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6VAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,0TAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;;;;;;8CAEhC,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6VAAC;4CAAK,WAAU;sDAAI,eAAe,IAAI;;;;;;;;;;;;8CAEzC,6VAAC,kTAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6VAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,OAAM;kCAEL,QAAQ,GAAG,CAAC,CAAC,uBACZ,6VAAC,4IAAA,CAAA,mBAAgB;gCAEf,UAAU,IAAM,mBAAmB;;oCAElC,OAAO,IAAI;oCAAE;oCACb,OAAO,EAAE,KAAK,eAAe,EAAE,kBAC9B,6VAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;+BALd,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAc9B", "debugId": null}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/app-sidebar.tsx"], "sourcesContent": ["'use client';\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger\r\n} from '@/components/ui/collapsible';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarRail\r\n} from '@/components/ui/sidebar';\r\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\r\nimport { navItems } from '@/constants/data';\r\nimport { useMediaQuery } from '@/hooks/use-media-query';\r\nimport { useRole } from '@/lib/role-context';\r\nimport { useUser } from '@clerk/nextjs';\r\nimport {\r\n  IconBell,\r\n  IconChevronRight,\r\n  IconChevronsDown,\r\n  IconCreditCard,\r\n  IconLogout,\r\n  IconPhotoUp,\r\n  IconUserCircle\r\n} from '@tabler/icons-react';\r\nimport { SignOutButton } from '@clerk/nextjs';\r\nimport Link from 'next/link';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport * as React from 'react';\r\nimport { Icons } from '../icons';\r\nimport { OrgSwitcher } from '../org-switcher';\r\nexport const company = {\r\n  name: 'Acme Inc',\r\n  logo: IconPhotoUp,\r\n  plan: 'Enterprise'\r\n};\r\n\r\nconst tenants = [\r\n  { id: '1', name: 'Acme Inc' },\r\n  { id: '2', name: 'Beta Corp' },\r\n  { id: '3', name: 'Gamma Ltd' }\r\n];\r\n\r\nexport default function AppSidebar() {\r\n  const pathname = usePathname();\r\n  const { isOpen } = useMediaQuery();\r\n  const { user } = useUser();\r\n  const { user: roleUser } = useRole();\r\n  const router = useRouter();\r\n  const handleSwitchTenant = (_tenantId: string) => {\r\n    // Tenant switching functionality would be implemented here\r\n  };\r\n\r\n  const activeTenant = tenants[0];\r\n\r\n  // Filter navigation items based on user role\r\n  const filteredNavItems = React.useMemo(() => {\r\n    if (!roleUser?.role) return navItems;\r\n\r\n    const userRole = roleUser.role;\r\n    return navItems.filter(item => {\r\n      // If no roles specified, show to all users\r\n      if (!item.roles || item.roles.length === 0) return true;\r\n\r\n      // Check if user's role is in the allowed roles\r\n      return item.roles.includes(userRole);\r\n    });\r\n  }, [roleUser?.role]);\r\n\r\n  React.useEffect(() => {\r\n    // Side effects based on sidebar state changes\r\n  }, [isOpen]);\r\n\r\n  return (\r\n    <Sidebar collapsible='icon'>\r\n      <SidebarHeader>\r\n        <OrgSwitcher\r\n          tenants={tenants}\r\n          defaultTenant={activeTenant}\r\n          onTenantSwitch={handleSwitchTenant}\r\n        />\r\n      </SidebarHeader>\r\n      <SidebarContent className='overflow-x-hidden'>\r\n        <SidebarGroup>\r\n          <SidebarGroupLabel>概览</SidebarGroupLabel>\r\n          <SidebarMenu>\r\n            {filteredNavItems.map((item) => {\r\n              const Icon = item.icon ? Icons[item.icon] : Icons.logo;\r\n              return item?.items && item?.items?.length > 0 ? (\r\n                <Collapsible\r\n                  key={item.title}\r\n                  asChild\r\n                  defaultOpen={item.isActive}\r\n                  className='group/collapsible'\r\n                >\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton\r\n                        tooltip={item.title}\r\n                        isActive={pathname === item.url}\r\n                      >\r\n                        {item.icon && <Icon />}\r\n                        <span>{item.title}</span>\r\n                        <IconChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent>\r\n                      <SidebarMenuSub>\r\n                        {item.items?.map((subItem) => (\r\n                          <SidebarMenuSubItem key={subItem.title}>\r\n                            <SidebarMenuSubButton\r\n                              asChild\r\n                              isActive={pathname === subItem.url}\r\n                            >\r\n                              <Link href={subItem.url}>\r\n                                <span>{subItem.title}</span>\r\n                              </Link>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>\r\n                        ))}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible>\r\n              ) : (\r\n                <SidebarMenuItem key={item.title}>\r\n                  <SidebarMenuButton\r\n                    asChild\r\n                    tooltip={item.title}\r\n                    isActive={pathname === item.url}\r\n                  >\r\n                    <Link href={item.url}>\r\n                      <Icon />\r\n                      <span>{item.title}</span>\r\n                    </Link>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>\r\n              );\r\n            })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <SidebarMenuButton\r\n                  size='lg'\r\n                  className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'\r\n                >\r\n                  {user && (\r\n                    <UserAvatarProfile\r\n                      className='h-8 w-8 rounded-lg'\r\n                      showInfo\r\n                      user={user}\r\n                    />\r\n                  )}\r\n                  <IconChevronsDown className='ml-auto size-4' />\r\n                </SidebarMenuButton>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent\r\n                className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg'\r\n                side='bottom'\r\n                align='end'\r\n                sideOffset={4}\r\n              >\r\n                <DropdownMenuLabel className='p-0 font-normal'>\r\n                  <div className='px-1 py-1.5'>\r\n                    {user && (\r\n                      <UserAvatarProfile\r\n                        className='h-8 w-8 rounded-lg'\r\n                        showInfo\r\n                        user={user}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                </DropdownMenuLabel>\r\n                <DropdownMenuSeparator />\r\n\r\n                <DropdownMenuGroup>\r\n                  <DropdownMenuItem\r\n                    onClick={() => router.push('/dashboard/profile')}\r\n                  >\r\n                    <IconUserCircle className='mr-2 h-4 w-4' />\r\n                    Profile\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <IconCreditCard className='mr-2 h-4 w-4' />\r\n                    Billing\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem>\r\n                    <IconBell className='mr-2 h-4 w-4' />\r\n                    Notifications\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuGroup>\r\n                <DropdownMenuSeparator />\r\n                <DropdownMenuItem>\r\n                  <IconLogout className='mr-2 h-4 w-4' />\r\n                  <SignOutButton redirectUrl='/auth/sign-in' />\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAKA;AASA;AAeA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AAjDA;;;;;;;;;;;;;;;;;AAkDO,MAAM,UAAU;IACrB,MAAM;IACN,MAAM,0TAAA,CAAA,cAAW;IACjB,MAAM;AACR;AAEA,MAAM,UAAU;IACd;QAAE,IAAI;QAAK,MAAM;IAAW;IAC5B;QAAE,IAAI;QAAK,MAAM;IAAY;IAC7B;QAAE,IAAI;QAAK,MAAM;IAAY;CAC9B;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,iPAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,qBAAqB,CAAC;IAC1B,2DAA2D;IAC7D;IAEA,MAAM,eAAe,OAAO,CAAC,EAAE;IAE/B,6CAA6C;IAC7C,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,UAAa,AAAD,EAAE;QACrC,IAAI,CAAC,UAAU,MAAM,OAAO,wHAAA,CAAA,WAAQ;QAEpC,MAAM,WAAW,SAAS,IAAI;QAC9B,OAAO,wHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA;YACrB,2CAA2C;YAC3C,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG,OAAO;YAEnD,+CAA+C;YAC/C,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC7B;IACF,GAAG;QAAC,UAAU;KAAK;IAEnB,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;IACd,8CAA8C;IAChD,GAAG;QAAC;KAAO;IAEX,qBACE,6VAAC,mIAAA,CAAA,UAAO;QAAC,aAAY;;0BACnB,6VAAC,mIAAA,CAAA,gBAAa;0BACZ,cAAA,6VAAC,qIAAA,CAAA,cAAW;oBACV,SAAS;oBACT,eAAe;oBACf,gBAAgB;;;;;;;;;;;0BAGpB,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6VAAC,mIAAA,CAAA,eAAY;;sCACX,6VAAC,mIAAA,CAAA,oBAAiB;sCAAC;;;;;;sCACnB,6VAAC,mIAAA,CAAA,cAAW;sCACT,iBAAiB,GAAG,CAAC,CAAC;gCACrB,MAAM,OAAO,KAAK,IAAI,GAAG,2HAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,GAAG,2HAAA,CAAA,QAAK,CAAC,IAAI;gCACtD,OAAO,MAAM,SAAS,MAAM,OAAO,SAAS,kBAC1C,6VAAC,uIAAA,CAAA,cAAW;oCAEV,OAAO;oCACP,aAAa,KAAK,QAAQ;oCAC1B,WAAU;8CAEV,cAAA,6VAAC,mIAAA,CAAA,kBAAe;;0DACd,6VAAC,uIAAA,CAAA,qBAAkB;gDAAC,OAAO;0DACzB,cAAA,6VAAC,mIAAA,CAAA,oBAAiB;oDAChB,SAAS,KAAK,KAAK;oDACnB,UAAU,aAAa,KAAK,GAAG;;wDAE9B,KAAK,IAAI,kBAAI,6VAAC;;;;;sEACf,6VAAC;sEAAM,KAAK,KAAK;;;;;;sEACjB,6VAAC,oUAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGhC,6VAAC,uIAAA,CAAA,qBAAkB;0DACjB,cAAA,6VAAC,mIAAA,CAAA,iBAAc;8DACZ,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6VAAC,mIAAA,CAAA,qBAAkB;sEACjB,cAAA,6VAAC,mIAAA,CAAA,uBAAoB;gEACnB,OAAO;gEACP,UAAU,aAAa,QAAQ,GAAG;0EAElC,cAAA,6VAAC,2QAAA,CAAA,UAAI;oEAAC,MAAM,QAAQ,GAAG;8EACrB,cAAA,6VAAC;kFAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;2DAND,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mCAnBzC,KAAK,KAAK;;;;yDAmCjB,6VAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,6VAAC,mIAAA,CAAA,oBAAiB;wCAChB,OAAO;wCACP,SAAS,KAAK,KAAK;wCACnB,UAAU,aAAa,KAAK,GAAG;kDAE/B,cAAA,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAM,KAAK,GAAG;;8DAClB,6VAAC;;;;;8DACD,6VAAC;8DAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;mCARD,KAAK,KAAK;;;;;4BAapC;;;;;;;;;;;;;;;;;0BAIN,6VAAC,mIAAA,CAAA,gBAAa;0BACZ,cAAA,6VAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6VAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,6VAAC,4IAAA,CAAA,eAAY;;8CACX,6VAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6VAAC,mIAAA,CAAA,oBAAiB;wCAChB,MAAK;wCACL,WAAU;;4CAET,sBACC,6VAAC,+IAAA,CAAA,oBAAiB;gDAChB,WAAU;gDACV,QAAQ;gDACR,MAAM;;;;;;0DAGV,6VAAC,oUAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGhC,6VAAC,4IAAA,CAAA,sBAAmB;oCAClB,WAAU;oCACV,MAAK;oCACL,OAAM;oCACN,YAAY;;sDAEZ,6VAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6VAAC;gDAAI,WAAU;0DACZ,sBACC,6VAAC,+IAAA,CAAA,oBAAiB;oDAChB,WAAU;oDACV,QAAQ;oDACR,MAAM;;;;;;;;;;;;;;;;sDAKd,6VAAC,4IAAA,CAAA,wBAAqB;;;;;sDAEtB,6VAAC,4IAAA,CAAA,oBAAiB;;8DAChB,6VAAC,4IAAA,CAAA,mBAAgB;oDACf,SAAS,IAAM,OAAO,IAAI,CAAC;;sEAE3B,6VAAC,gUAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG7C,6VAAC,4IAAA,CAAA,mBAAgB;;sEACf,6VAAC,gUAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG7C,6VAAC,4IAAA,CAAA,mBAAgB;;sEACf,6VAAC,oTAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAIzC,6VAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,6VAAC,4IAAA,CAAA,mBAAgB;;8DACf,6VAAC,wTAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6VAAC,qRAAA,CAAA,gBAAa;oDAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6VAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 3467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<'nav'>) {\r\n  return <nav aria-label='breadcrumb' data-slot='breadcrumb' {...props} />;\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<'ol'>) {\r\n  return (\r\n    <ol\r\n      data-slot='breadcrumb-list'\r\n      className={cn(\r\n        'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='breadcrumb-item'\r\n      className={cn('inline-flex items-center gap-1.5', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='breadcrumb-link'\r\n      className={cn('hover:text-foreground transition-colors', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='breadcrumb-page'\r\n      role='link'\r\n      aria-disabled='true'\r\n      aria-current='page'\r\n      className={cn('text-foreground font-normal', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot='breadcrumb-separator'\r\n      role='presentation'\r\n      aria-hidden='true'\r\n      className={cn('[&>svg]:size-3.5', className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  );\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='breadcrumb-ellipsis'\r\n      role='presentation'\r\n      aria-hidden='true'\r\n      className={cn('flex size-9 items-center justify-center', className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className='size-4' />\r\n      <span className='sr-only'>More</span>\r\n    </span>\r\n  );\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6VAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6VAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6VAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6VAAC,0SAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6VAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6VAAC,oSAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6VAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 3599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-breadcrumbs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePathname } from 'next/navigation';\r\nimport { useMemo } from 'react';\r\n\r\ntype BreadcrumbItem = {\r\n  title: string;\r\n  link: string;\r\n};\r\n\r\n// This allows to add custom title as well\r\nconst routeMapping: Record<string, BreadcrumbItem[]> = {\r\n  '/dashboard': [{ title: 'Dashboard', link: '/dashboard' }],\r\n  '/dashboard/employee': [\r\n    { title: 'Dashboard', link: '/dashboard' },\r\n    { title: 'Employee', link: '/dashboard/employee' }\r\n  ],\r\n  '/dashboard/product': [\r\n    { title: 'Dashboard', link: '/dashboard' },\r\n    { title: 'Product', link: '/dashboard/product' }\r\n  ]\r\n  // Add more custom mappings as needed\r\n};\r\n\r\nexport function useBreadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  const breadcrumbs = useMemo(() => {\r\n    // Check if we have a custom mapping for this exact path\r\n    if (routeMapping[pathname]) {\r\n      return routeMapping[pathname];\r\n    }\r\n\r\n    // If no exact match, fall back to generating breadcrumbs from the path\r\n    const segments = pathname.split('/').filter(Boolean);\r\n    return segments.map((segment, index) => {\r\n      const path = `/${segments.slice(0, index + 1).join('/')}`;\r\n      return {\r\n        title: segment.charAt(0).toUpperCase() + segment.slice(1),\r\n        link: path\r\n      };\r\n    });\r\n  }, [pathname]);\r\n\r\n  return breadcrumbs;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAUA,0CAA0C;AAC1C,MAAM,eAAiD;IACrD,cAAc;QAAC;YAAE,OAAO;YAAa,MAAM;QAAa;KAAE;IAC1D,uBAAuB;QACrB;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAY,MAAM;QAAsB;KAClD;IACD,sBAAsB;QACpB;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAW,MAAM;QAAqB;KAChD;AAEH;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,iPAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,wDAAwD;QACxD,IAAI,YAAY,CAAC,SAAS,EAAE;YAC1B,OAAO,YAAY,CAAC,SAAS;QAC/B;QAEA,uEAAuE;QACvE,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAC5C,OAAO,SAAS,GAAG,CAAC,CAAC,SAAS;YAC5B,MAAM,OAAO,CAAC,CAAC,EAAE,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM;YACzD,OAAO;gBACL,OAAO,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;gBACvD,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAS;IAEb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/breadcrumbs.tsx"], "sourcesContent": ["'use client';\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator\r\n} from '@/components/ui/breadcrumb';\r\nimport { useBreadcrumbs } from '@/hooks/use-breadcrumbs';\r\nimport { IconSlash } from '@tabler/icons-react';\r\nimport { Fragment } from 'react';\r\n\r\nexport function Breadcrumbs() {\r\n  const items = useBreadcrumbs();\r\n  if (items.length === 0) return null;\r\n\r\n  return (\r\n    <Breadcrumb>\r\n      <BreadcrumbList>\r\n        {items.map((item, index) => (\r\n          <Fragment key={item.title}>\r\n            {index !== items.length - 1 && (\r\n              <BreadcrumbItem className='hidden md:block'>\r\n                <BreadcrumbLink href={item.link}>{item.title}</BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n            )}\r\n            {index < items.length - 1 && (\r\n              <BreadcrumbSeparator className='hidden md:block'>\r\n                <IconSlash />\r\n              </BreadcrumbSeparator>\r\n            )}\r\n            {index === items.length - 1 && (\r\n              <BreadcrumbPage>{item.title}</BreadcrumbPage>\r\n            )}\r\n          </Fragment>\r\n        ))}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAQA;AACA;AACA;AAXA;;;;;;AAaO,SAAS;IACd,MAAM,QAAQ,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD;IAC3B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,6VAAC,sIAAA,CAAA,aAAU;kBACT,cAAA,6VAAC,sIAAA,CAAA,iBAAc;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6VAAC,oTAAA,CAAA,WAAQ;;wBACN,UAAU,MAAM,MAAM,GAAG,mBACxB,6VAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACxB,cAAA,6VAAC,sIAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,IAAI;0CAAG,KAAK,KAAK;;;;;;;;;;;wBAG/C,QAAQ,MAAM,MAAM,GAAG,mBACtB,6VAAC,sIAAA,CAAA,sBAAmB;4BAAC,WAAU;sCAC7B,cAAA,6VAAC,sTAAA,CAAA,YAAS;;;;;;;;;;wBAGb,UAAU,MAAM,MAAM,GAAG,mBACxB,6VAAC,sIAAA,CAAA,iBAAc;sCAAE,KAAK,KAAK;;;;;;;mBAZhB,KAAK,KAAK;;;;;;;;;;;;;;;AAmBnC", "debugId": null}}, {"offset": {"line": 3741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/search-input.tsx"], "sourcesContent": ["'use client';\r\nimport { useKBar } from 'kbar';\r\nimport { IconSearch } from '@tabler/icons-react';\r\nimport { Button } from './ui/button';\r\n\r\nexport default function SearchInput() {\r\n  const { query } = useKBar();\r\n  return (\r\n    <div className='w-full space-y-2'>\r\n      <Button\r\n        variant='outline'\r\n        className='bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64'\r\n        onClick={query.toggle}\r\n      >\r\n        <IconSearch className='mr-2 h-4 w-4' />\r\n        Search...\r\n        <kbd className='bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex'>\r\n          <span className='text-xs'>⌘</span>K\r\n        </kbd>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mPAAA,CAAA,UAAO,AAAD;IACxB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,WAAU;YACV,SAAS,MAAM,MAAM;;8BAErB,6VAAC,wTAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAAiB;8BAEvC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAK,WAAU;sCAAU;;;;;;wBAAQ;;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 3806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/user-nav.tsx"], "sourcesContent": ["'use client';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger\r\n} from '@/components/ui/dropdown-menu';\r\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\r\nimport { SignOutButton, useUser } from '@clerk/nextjs';\r\nimport { useRouter } from 'next/navigation';\r\nexport function UserNav() {\r\n  const { user } = useUser();\r\n  const router = useRouter();\r\n  if (user) {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant='ghost' className='relative h-8 w-8 rounded-full'>\r\n            <UserAvatarProfile user={user} />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent\r\n          className='w-56'\r\n          align='end'\r\n          sideOffset={10}\r\n          forceMount\r\n        >\r\n          <DropdownMenuLabel className='font-normal'>\r\n            <div className='flex flex-col space-y-1'>\r\n              <p className='text-sm leading-none font-medium'>\r\n                {user.fullName}\r\n              </p>\r\n              <p className='text-muted-foreground text-xs leading-none'>\r\n                {user.emailAddresses[0].emailAddress}\r\n              </p>\r\n            </div>\r\n          </DropdownMenuLabel>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuGroup>\r\n            <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>\r\n              Profile\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem>Billing</DropdownMenuItem>\r\n            <DropdownMenuItem>Settings</DropdownMenuItem>\r\n            <DropdownMenuItem>New Team</DropdownMenuItem>\r\n          </DropdownMenuGroup>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem>\r\n            <SignOutButton redirectUrl='/auth/sign-in' />\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AASA;AACA;AAAA;AACA;AAbA;;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,IAAI,MAAM;QACR,qBACE,6VAAC,4IAAA,CAAA,eAAY;;8BACX,6VAAC,4IAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;kCAChC,cAAA,6VAAC,+IAAA,CAAA,oBAAiB;4BAAC,MAAM;;;;;;;;;;;;;;;;8BAG7B,6VAAC,4IAAA,CAAA,sBAAmB;oBAClB,WAAU;oBACV,OAAM;oBACN,YAAY;oBACZ,UAAU;;sCAEV,6VAAC,4IAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC3B,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAE,WAAU;kDACV,KAAK,QAAQ;;;;;;kDAEhB,6VAAC;wCAAE,WAAU;kDACV,KAAK,cAAc,CAAC,EAAE,CAAC,YAAY;;;;;;;;;;;;;;;;;sCAI1C,6VAAC,4IAAA,CAAA,wBAAqB;;;;;sCACtB,6VAAC,4IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAuB;;;;;;8CAGpE,6VAAC,4IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,4IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,4IAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;sCAEpB,6VAAC,4IAAA,CAAA,wBAAqB;;;;;sCACtB,6VAAC,4IAAA,CAAA,mBAAgB;sCACf,cAAA,6VAAC,qRAAA,CAAA,gBAAa;gCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;;IAKrC;AACF", "debugId": null}}, {"offset": {"line": 3969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot='label'\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6VAAC,8QAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot='select' {...props} />;\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot='select-group' {...props} />;\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot='select-value' {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot='select-trigger'\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className='size-4 opacity-50' />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot='select-content'\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot='select-label'\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot='select-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className='size-4' />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot='select-separator'\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot='select-scroll-up-button'\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className='size-4' />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot='select-scroll-down-button'\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className='size-4' />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6VAAC,+QAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6VAAC,+QAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6VAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6VAAC,4SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6VAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6VAAC,+QAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6VAAC;;;;;8BACD,6VAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6VAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6VAAC;gBAAK,WAAU;0BACd,cAAA,6VAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6VAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6VAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6VAAC,+QAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6VAAC,+QAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,wSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6VAAC,+QAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,4SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 4222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/theme-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useThemeConfig } from '@/components/active-theme';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue\r\n} from '@/components/ui/select';\r\n\r\nconst DEFAULT_THEMES = [\r\n  {\r\n    name: 'Default',\r\n    value: 'default'\r\n  },\r\n  {\r\n    name: 'Blue',\r\n    value: 'blue'\r\n  },\r\n  {\r\n    name: 'Green',\r\n    value: 'green'\r\n  },\r\n  {\r\n    name: 'Amber',\r\n    value: 'amber'\r\n  }\r\n];\r\n\r\nconst SCALED_THEMES = [\r\n  {\r\n    name: 'Default',\r\n    value: 'default-scaled'\r\n  },\r\n  {\r\n    name: 'Blue',\r\n    value: 'blue-scaled'\r\n  }\r\n];\r\n\r\nconst MONO_THEMES = [\r\n  {\r\n    name: 'Mono',\r\n    value: 'mono-scaled'\r\n  }\r\n];\r\n\r\nexport function ThemeSelector() {\r\n  const { activeTheme, setActiveTheme } = useThemeConfig();\r\n\r\n  return (\r\n    <div className='flex items-center gap-2'>\r\n      <Label htmlFor='theme-selector' className='sr-only'>\r\n        Theme\r\n      </Label>\r\n      <Select value={activeTheme} onValueChange={setActiveTheme}>\r\n        <SelectTrigger\r\n          id='theme-selector'\r\n          className='justify-start *:data-[slot=select-value]:w-12'\r\n        >\r\n          <span className='text-muted-foreground hidden sm:block'>\r\n            Select a theme:\r\n          </span>\r\n          <span className='text-muted-foreground block sm:hidden'>Theme</span>\r\n          <SelectValue placeholder='Select a theme' />\r\n        </SelectTrigger>\r\n        <SelectContent align='end'>\r\n          <SelectGroup>\r\n            <SelectLabel>Default</SelectLabel>\r\n            {DEFAULT_THEMES.map((theme) => (\r\n              <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectGroup>\r\n          <SelectSeparator />\r\n          <SelectGroup>\r\n            <SelectLabel>Scaled</SelectLabel>\r\n            {SCALED_THEMES.map((theme) => (\r\n              <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectGroup>\r\n          <SelectGroup>\r\n            <SelectLabel>Monospaced</SelectLabel>\r\n            {MONO_THEMES.map((theme) => (\r\n              <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectGroup>\r\n        </SelectContent>\r\n      </Select>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAErD,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAiB,WAAU;0BAAU;;;;;;0BAGpD,6VAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAa,eAAe;;kCACzC,6VAAC,kIAAA,CAAA,gBAAa;wBACZ,IAAG;wBACH,WAAU;;0CAEV,6VAAC;gCAAK,WAAU;0CAAwC;;;;;;0CAGxD,6VAAC;gCAAK,WAAU;0CAAwC;;;;;;0CACxD,6VAAC,kIAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;;kCAE3B,6VAAC,kIAAA,CAAA,gBAAa;wBAAC,OAAM;;0CACnB,6VAAC,kIAAA,CAAA,cAAW;;kDACV,6VAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6VAAC,kIAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;0CAK/B,6VAAC,kIAAA,CAAA,kBAAe;;;;;0CAChB,6VAAC,kIAAA,CAAA,cAAW;;kDACV,6VAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6VAAC,kIAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;0CAK/B,6VAAC,kIAAA,CAAA,cAAW;;kDACV,6VAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;oCACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6VAAC,kIAAA,CAAA,aAAU;4CAAkB,OAAO,MAAM,KAAK;sDAC5C,MAAM,IAAI;2CADI,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC", "debugId": null}}, {"offset": {"line": 4421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/layout/ThemeToggle/theme-toggle.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { IconBrightness } from '@tabler/icons-react';\r\nimport { useTheme } from 'next-themes';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\n\r\nexport function ModeToggle() {\r\n  const { setTheme, resolvedTheme } = useTheme();\r\n\r\n  const handleThemeToggle = React.useCallback(\r\n    (e?: React.MouseEvent) => {\r\n      const newMode = resolvedTheme === 'dark' ? 'light' : 'dark';\r\n      const root = document.documentElement;\r\n\r\n      if (!document.startViewTransition) {\r\n        setTheme(newMode);\r\n        return;\r\n      }\r\n\r\n      // Set coordinates from the click event\r\n      if (e) {\r\n        root.style.setProperty('--x', `${e.clientX}px`);\r\n        root.style.setProperty('--y', `${e.clientY}px`);\r\n      }\r\n\r\n      document.startViewTransition(() => {\r\n        setTheme(newMode);\r\n      });\r\n    },\r\n    [resolvedTheme, setTheme]\r\n  );\r\n\r\n  return (\r\n    <Button\r\n      variant='secondary'\r\n      size='icon'\r\n      className='group/toggle size-8'\r\n      onClick={handleThemeToggle}\r\n    >\r\n      <IconBrightness />\r\n      <span className='sr-only'>Toggle theme</span>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAiB,AAAD,EACxC,CAAC;QACC,MAAM,UAAU,kBAAkB,SAAS,UAAU;QACrD,MAAM,OAAO,SAAS,eAAe;QAErC,IAAI,CAAC,SAAS,mBAAmB,EAAE;YACjC,SAAS;YACT;QACF;QAEA,uCAAuC;QACvC,IAAI,GAAG;YACL,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9C,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;QAChD;QAEA,SAAS,mBAAmB,CAAC;YAC3B,SAAS;QACX;IACF,GACA;QAAC;QAAe;KAAS;IAG3B,qBACE,6VAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAU;QACV,SAAS;;0BAET,6VAAC,gUAAA,CAAA,iBAAc;;;;;0BACf,6VAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}]}