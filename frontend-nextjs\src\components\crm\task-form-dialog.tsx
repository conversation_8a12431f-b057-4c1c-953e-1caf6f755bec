'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { IconCalendar, IconClock, IconUser } from '@/components/icons';
import { PatientTask, PatientTaskFormD<PERSON>, User } from '@/types/clinic';
import { formatDateTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

const taskSchema = z.object({
  taskType: z.enum(['follow-up-call', 'appointment-scheduling', 'treatment-reminder', 'billing-follow-up', 'medical-record-update', 'consultation-follow-up']),
  title: z.string().min(1, '请输入任务标题').max(200, '标题不能超过200个字符'),
  description: z.string().optional(),
  assignedTo: z.string().min(1, '请选择负责人'),
  dueDate: z.string().min(1, '请选择截止日期'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  status: z.enum(['pending', 'in-progress', 'completed', 'cancelled']).default('pending'),
  relatedInteraction: z.string().optional(),
  completionNotes: z.string().optional(),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patientId: string;
  patientName: string;
  task?: PatientTask;
  defaultType?: string;
  relatedInteractionId?: string;
  availableStaff: User[];
  onSubmit: (data: PatientTaskFormData) => Promise<void>;
  loading?: boolean;
}

const taskTypeOptions = [
  { value: 'follow-up-call', label: '跟进电话', description: '安排跟进电话任务' },
  { value: 'appointment-scheduling', label: '预约安排', description: '安排预约相关任务' },
  { value: 'treatment-reminder', label: '治疗提醒', description: '创建治疗提醒任务' },
  { value: 'billing-follow-up', label: '账单跟进', description: '创建账单跟进任务' },
  { value: 'medical-record-update', label: '病历更新', description: '安排病历更新任务' },
  { value: 'consultation-follow-up', label: '咨询跟进', description: '创建咨询跟进任务' },
];

const priorityOptions = [
  { value: 'low', label: '低', description: '一般重要性', color: 'text-gray-600' },
  { value: 'medium', label: '中', description: '中等重要性', color: 'text-yellow-600' },
  { value: 'high', label: '高', description: '高重要性，需要优先处理', color: 'text-orange-600' },
  { value: 'urgent', label: '紧急', description: '紧急任务，需要立即处理', color: 'text-red-600' },
];

const statusOptions = [
  { value: 'pending', label: '待处理', description: '新创建的任务' },
  { value: 'in-progress', label: '进行中', description: '正在处理中' },
  { value: 'completed', label: '已完成', description: '任务已完成' },
  { value: 'cancelled', label: '已取消', description: '任务已取消' },
];

export function TaskFormDialog({
  open,
  onOpenChange,
  patientId,
  patientName,
  task,
  defaultType,
  relatedInteractionId,
  availableStaff,
  onSubmit,
  loading = false,
}: TaskFormDialogProps) {
  const [dueDate, setDueDate] = useState<Date | undefined>();
  const [dueTime, setDueTime] = useState('09:00');

  const isEditing = !!task;

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      taskType: (defaultType as any) || 'follow-up-call',
      title: '',
      description: '',
      assignedTo: '',
      dueDate: '',
      priority: 'medium',
      status: 'pending',
      relatedInteraction: relatedInteractionId || '',
      completionNotes: '',
    },
  });

  // Reset form when dialog opens/closes or task changes
  useEffect(() => {
    if (open) {
      if (task) {
        form.reset({
          taskType: task.taskType,
          title: task.title,
          description: task.description || '',
          assignedTo: typeof task.assignedTo === 'string' ? task.assignedTo : task.assignedTo.id,
          dueDate: task.dueDate,
          priority: task.priority,
          status: task.status,
          relatedInteraction: typeof task.relatedInteraction === 'string' ? task.relatedInteraction : task.relatedInteraction?.id || '',
          completionNotes: task.completionNotes || '',
        });
        
        const date = new Date(task.dueDate);
        setDueDate(date);
        setDueTime(date.toTimeString().slice(0, 5));
      } else {
        // Set default due date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        setDueDate(tomorrow);
        
        form.reset({
          taskType: (defaultType as any) || 'follow-up-call',
          title: '',
          description: '',
          assignedTo: '',
          dueDate: '',
          priority: 'medium',
          status: 'pending',
          relatedInteraction: relatedInteractionId || '',
          completionNotes: '',
        });
        setDueTime('09:00');
      }
    }
  }, [open, task, defaultType, relatedInteractionId, form]);

  const handleSubmit = async (data: TaskFormData) => {
    try {
      // Combine due date and time
      if (dueDate) {
        const [hours, minutes] = dueTime.split(':');
        const combinedDate = new Date(dueDate);
        combinedDate.setHours(parseInt(hours), parseInt(minutes));
        data.dueDate = combinedDate.toISOString();
      }

      const formData: PatientTaskFormData = {
        ...data,
        patient: patientId,
      };

      await onSubmit(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting task:', error);
    }
  };

  const watchStatus = form.watch('status');
  const watchTaskType = form.watch('taskType');

  // Generate default title based on task type
  useEffect(() => {
    if (!isEditing && watchTaskType) {
      const typeConfig = taskTypeOptions.find(opt => opt.value === watchTaskType);
      if (typeConfig) {
        form.setValue('title', `${typeConfig.label} - ${patientName}`);
      }
    }
  }, [watchTaskType, patientName, isEditing, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑任务' : '创建任务'}
          </DialogTitle>
          <DialogDescription>
            为患者 <strong>{patientName}</strong> {isEditing ? '编辑' : '创建'}跟进任务
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Task Type */}
            <FormField
              control={form.control}
              name="taskType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>任务类型 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择任务类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {taskTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>任务标题 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="简要描述任务内容"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    请输入简洁明了的任务标题
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>任务描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="详细描述任务要求和注意事项..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    详细描述任务的具体要求和执行步骤
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Assigned To */}
            <FormField
              control={form.control}
              name="assignedTo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分配给 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择负责人">
                          <div className="flex items-center gap-2">
                            <IconUser className="size-4" />
                            <span>选择负责人</span>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableStaff.map((staff) => (
                        <SelectItem key={staff.id} value={staff.id}>
                          <div className="flex items-center gap-2">
                            <IconUser className="size-4" />
                            <div>
                              <div className="font-medium">
                                {`${staff.firstName || ''} ${staff.lastName || ''}`.trim() || staff.email}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {staff.role === 'admin' && '管理员'}
                                {staff.role === 'doctor' && '医生'}
                                {staff.role === 'front-desk' && '前台'}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Due Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <FormLabel>截止日期 *</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dueDate && "text-muted-foreground"
                      )}
                    >
                      <IconCalendar className="mr-2 h-4 w-4" />
                      {dueDate ? formatDateTime(dueDate) : "选择日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={dueDate}
                      onSelect={setDueDate}
                      disabled={(date) => date < new Date()}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <FormLabel>截止时间</FormLabel>
                <div className="relative">
                  <IconClock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="time"
                    value={dueTime}
                    onChange={(e) => setDueTime(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Priority and Status */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>优先级</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {priorityOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className={cn("font-medium", option.color)}>{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>状态</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Completion Notes - only show if status is completed */}
            {watchStatus === 'completed' && (
              <FormField
                control={form.control}
                name="completionNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>完成备注</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="记录任务完成情况和总结..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      记录任务完成的具体情况和总结
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? '保存中...' : isEditing ? '更新' : '创建'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
