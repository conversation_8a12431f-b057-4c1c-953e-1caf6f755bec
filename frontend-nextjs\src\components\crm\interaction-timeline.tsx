'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  IconPhone, 
  IconMail, 
  IconStethoscope, 
  IconUser, 
  IconMessageCircle, 
  IconCreditCard,
  IconSearch,
  IconFilter,
  IconPlus,
  IconClock,
  IconArrowRight
} from '@/components/icons';
import { PatientInteraction, User } from '@/types/clinic';
import { formatDateTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface InteractionTimelineProps {
  patientId: string;
  interactions: PatientInteraction[];
  loading?: boolean;
  onCreateInteraction?: () => void;
  onInteractionClick?: (interaction: PatientInteraction) => void;
  className?: string;
}

const interactionTypeConfig = {
  'phone-call': {
    label: '电话通话',
    icon: IconPhone,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  'email': {
    label: '邮件沟通',
    icon: IconMail,
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  'consultation-note': {
    label: '咨询记录',
    icon: IconStethoscope,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  'in-person-visit': {
    label: '到院就诊',
    icon: IconUser,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
  },
  'treatment-discussion': {
    label: '治疗讨论',
    icon: IconMessageCircle,
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  },
  'billing-inquiry': {
    label: '账单咨询',
    icon: IconCreditCard,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
};

const statusConfig = {
  'open': { label: '开放', color: 'bg-gray-100 text-gray-800' },
  'in-progress': { label: '进行中', color: 'bg-blue-100 text-blue-800' },
  'resolved': { label: '已解决', color: 'bg-green-100 text-green-800' },
  'closed': { label: '已关闭', color: 'bg-gray-100 text-gray-600' },
};

const priorityConfig = {
  'low': { label: '低', color: 'bg-gray-100 text-gray-600' },
  'medium': { label: '中', color: 'bg-yellow-100 text-yellow-800' },
  'high': { label: '高', color: 'bg-red-100 text-red-800' },
};

export function InteractionTimeline({
  patientId,
  interactions,
  loading = false,
  onCreateInteraction,
  onInteractionClick,
  className,
}: InteractionTimelineProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filteredInteractions, setFilteredInteractions] = useState<PatientInteraction[]>(interactions);

  // Filter interactions based on search and filters
  useEffect(() => {
    let filtered = interactions;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(interaction =>
        interaction.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        interaction.outcome?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (typeof interaction.staffMember === 'object' && 
         `${interaction.staffMember.firstName} ${interaction.staffMember.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(interaction => interaction.interactionType === filterType);
    }

    // Status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(interaction => interaction.status === filterStatus);
    }

    setFilteredInteractions(filtered);
  }, [interactions, searchTerm, filterType, filterStatus]);

  const getInteractionIcon = (type: PatientInteraction['interactionType']) => {
    const IconComponent = interactionTypeConfig[type]?.icon || IconMessageCircle;
    return <IconComponent className="size-4" />;
  };

  const getStaffMemberName = (staffMember: User | string) => {
    if (typeof staffMember === 'string') return '未知工作人员';
    return `${staffMember.firstName || ''} ${staffMember.lastName || ''}`.trim() || staffMember.email;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconClock className="size-5" />
            互动时间线
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start gap-3">
                  <div className="size-8 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <IconClock className="size-5" />
            互动时间线
            <Badge variant="secondary" className="ml-2">
              {filteredInteractions.length}
            </Badge>
          </CardTitle>
          {onCreateInteraction && (
            <Button onClick={onCreateInteraction} size="sm">
              <IconPlus className="size-4 mr-2" />
              添加互动
            </Button>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            <Input
              placeholder="搜索互动记录..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <IconFilter className="size-4 mr-2" />
              <SelectValue placeholder="类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              {Object.entries(interactionTypeConfig).map(([value, config]) => (
                <SelectItem key={value} value={value}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full sm:w-[120px]">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              {Object.entries(statusConfig).map(([value, config]) => (
                <SelectItem key={value} value={value}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[600px]">
          {filteredInteractions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <IconMessageCircle className="size-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">暂无互动记录</p>
              <p className="text-sm">开始记录与患者的沟通互动</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredInteractions.map((interaction, index) => {
                const typeConfig = interactionTypeConfig[interaction.interactionType];
                const statusBadge = statusConfig[interaction.status];
                const priorityBadge = priorityConfig[interaction.priority];

                return (
                  <div key={interaction.id} className="relative">
                    {/* Timeline line */}
                    {index < filteredInteractions.length - 1 && (
                      <div className="absolute left-4 top-12 bottom-0 w-px bg-border" />
                    )}

                    <div 
                      className={cn(
                        "flex items-start gap-3 p-4 rounded-lg border transition-colors",
                        onInteractionClick && "cursor-pointer hover:bg-muted/50"
                      )}
                      onClick={() => onInteractionClick?.(interaction)}
                    >
                      {/* Icon */}
                      <div className={cn(
                        "flex items-center justify-center size-8 rounded-full border-2",
                        typeConfig?.color || 'bg-gray-100 text-gray-600 border-gray-200'
                      )}>
                        {getInteractionIcon(interaction.interactionType)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2 mb-2">
                          <h4 className="font-medium text-sm leading-tight">
                            {interaction.title}
                          </h4>
                          <div className="flex items-center gap-1 flex-shrink-0">
                            <Badge variant="outline" className={statusBadge.color}>
                              {statusBadge.label}
                            </Badge>
                            <Badge variant="outline" className={priorityBadge.color}>
                              {priorityBadge.label}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                          <span className="flex items-center gap-1">
                            <IconUser className="size-3" />
                            {getStaffMemberName(interaction.staffMember)}
                          </span>
                          <span className="flex items-center gap-1">
                            <IconClock className="size-3" />
                            {formatDateTime(new Date(interaction.timestamp))}
                          </span>
                        </div>

                        {interaction.outcome && (
                          <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                            {interaction.outcome}
                          </p>
                        )}

                        {interaction.followUpRequired && (
                          <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                            <IconArrowRight className="size-3" />
                            需要跟进
                            {interaction.followUpDate && (
                              <span>- {formatDateTime(new Date(interaction.followUpDate))}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
