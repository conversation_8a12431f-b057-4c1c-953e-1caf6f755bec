/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    treatments: Treatment;
    patients: Patient;
    appointments: Appointment;
    bills: Bill;
    'bill-items': BillItem;
    payments: Payment;
    deposits: Deposit;
    'patient-interactions': PatientInteraction;
    'patient-tasks': PatientTask;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    treatments: TreatmentsSelect<false> | TreatmentsSelect<true>;
    patients: PatientsSelect<false> | PatientsSelect<true>;
    appointments: AppointmentsSelect<false> | AppointmentsSelect<true>;
    bills: BillsSelect<false> | BillsSelect<true>;
    'bill-items': BillItemsSelect<false> | BillItemsSelect<true>;
    payments: PaymentsSelect<false> | PaymentsSelect<true>;
    deposits: DepositsSelect<false> | DepositsSelect<true>;
    'patient-interactions': PatientInteractionsSelect<false> | PatientInteractionsSelect<true>;
    'patient-tasks': PatientTasksSelect<false> | PatientTasksSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  /**
   * User role determines access permissions - only Admin can modify
   */
  role: 'admin' | 'front-desk' | 'doctor';
  /**
   * Unique identifier from Clerk authentication service
   */
  clerkId: string;
  firstName?: string | null;
  lastName?: string | null;
  /**
   * Automatically updated when user logs in
   */
  lastLogin?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "treatments".
 */
export interface Treatment {
  id: number;
  name: string;
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  defaultPrice: number;
  defaultDurationInMinutes: number;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patients".
 */
export interface Patient {
  id: number;
  /**
   * 区分咨询用户和正式患者
   */
  userType: 'consultation' | 'patient';
  /**
   * 用户当前状态
   */
  status: 'active' | 'inactive' | 'converted';
  /**
   * 用户来源追踪
   */
  source: 'walk-in' | 'referral' | 'online' | 'phone';
  /**
   * 如果是转介绍，记录转介绍人信息
   */
  referredBy?: string | null;
  fullName: string;
  phone: string;
  email?: string | null;
  photo?: (number | null) | Media;
  /**
   * 从咨询用户转为正式患者的时间
   */
  convertedAt?: string | null;
  /**
   * 最后一次就诊的时间
   */
  lastVisit?: string | null;
  /**
   * 紧急联系人信息（正式患者建议填写）
   */
  emergencyContact?: string | null;
  /**
   * 患者过敏史记录（正式患者重要信息）
   */
  allergies?:
    | {
        allergen: string;
        severity?: ('mild' | 'moderate' | 'severe') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * 患者主要病史摘要（正式患者重要信息）
   */
  medicalHistory?: string | null;
  /**
   * Confidential medical information - restricted to medical staff only
   */
  medicalNotes?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appointments".
 */
export interface Appointment {
  id: number;
  /**
   * 区分咨询预约和治疗预约
   */
  appointmentType: 'consultation' | 'treatment';
  appointmentDate: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  /**
   * 治疗预约必填，咨询预约可选
   */
  treatment?: (number | null) | Treatment;
  /**
   * 最终价格（咨询预约可能为0）
   */
  price?: number | null;
  durationInMinutes: number;
  patient: number | Patient;
  practitioner: number | User;
  /**
   * 仅咨询预约需要填写
   */
  consultationType?: ('initial' | 'follow-up' | 'price-inquiry') | null;
  /**
   * 咨询用户感兴趣的治疗项目
   */
  interestedTreatments?: (number | Treatment)[] | null;
  /**
   * 治疗预约的支付状态
   */
  paymentStatus?: ('pending' | 'partial' | 'paid' | 'overdue') | null;
  /**
   * 咨询预约的结果追踪
   */
  outcome?: ('converted' | 'scheduled-treatment' | 'no-interest' | 'follow-up-needed') | null;
  /**
   * 预约相关的备注信息
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bills".
 */
export interface Bill {
  id: number;
  /**
   * 系统自动生成，格式：BILL-YYYYMMDD-XXXXXX
   */
  billNumber: string;
  patient: number | Patient;
  /**
   * 如果账单来源于预约，请选择对应预约
   */
  appointment?: (number | null) | Appointment;
  /**
   * 如果是治疗账单，请选择对应治疗项目
   */
  treatment?: (number | null) | Treatment;
  billType: 'treatment' | 'consultation' | 'deposit' | 'additional';
  status: 'draft' | 'sent' | 'confirmed' | 'paid' | 'cancelled';
  /**
   * 税前金额
   */
  subtotal: number;
  /**
   * 折扣金额
   */
  discountAmount?: number | null;
  /**
   * 税费金额
   */
  taxAmount?: number | null;
  /**
   * 最终应付金额 = 小计 + 税费 - 折扣
   */
  totalAmount: number;
  /**
   * 已支付的金额
   */
  paidAmount?: number | null;
  /**
   * 剩余未支付金额（自动计算）
   */
  remainingAmount?: number | null;
  issueDate: string;
  /**
   * 账单到期日期
   */
  dueDate: string;
  /**
   * 账单完全支付的日期
   */
  paidDate?: string | null;
  /**
   * 账单的简要描述
   */
  description: string;
  /**
   * 账单相关的备注信息
   */
  notes?: string | null;
  /**
   * 创建此账单的工作人员
   */
  createdBy: number | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bill-items".
 */
export interface BillItem {
  id: number;
  bill: number | Bill;
  itemType: 'treatment' | 'consultation' | 'material' | 'service';
  /**
   * 关联的治疗或服务的ID（可选）
   */
  itemId?: string | null;
  /**
   * 账单项目的名称
   */
  itemName: string;
  /**
   * 项目的详细描述
   */
  description?: string | null;
  /**
   * 项目数量
   */
  quantity: number;
  /**
   * 项目单价
   */
  unitPrice: number;
  /**
   * 折扣率，0-100之间的数值
   */
  discountRate?: number | null;
  /**
   * 该项目的总金额（自动计算）
   */
  totalPrice?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments".
 */
export interface Payment {
  id: number;
  /**
   * 系统自动生成，格式：PAY-YYYYMMDD-XXXXXX
   */
  paymentNumber: string;
  bill: number | Bill;
  patient: number | Patient;
  /**
   * 本次支付的金额
   */
  amount: number;
  paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  /**
   * 第三方支付平台的交易ID（如适用）
   */
  transactionId?: string | null;
  paymentDate: string;
  /**
   * 处理此次支付的工作人员
   */
  receivedBy: number | User;
  /**
   * 如果此支付来自押金抵扣，请选择对应押金
   */
  relatedDeposit?: (number | null) | Deposit;
  /**
   * 支付相关的备注信息
   */
  notes?: string | null;
  /**
   * 收据编号（系统自动生成）
   */
  receiptNumber?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "deposits".
 */
export interface Deposit {
  id: number;
  /**
   * 系统自动生成，格式：DEP-YYYYMMDD-XXXXXX
   */
  depositNumber: string;
  patient: number | Patient;
  /**
   * 如果押金与特定预约相关
   */
  appointment?: (number | null) | Appointment;
  /**
   * 如果押金与特定治疗相关
   */
  treatment?: (number | null) | Treatment;
  depositType: 'treatment' | 'appointment' | 'material';
  /**
   * 押金总金额
   */
  amount: number;
  status: 'active' | 'used' | 'refunded' | 'expired';
  /**
   * 已使用的押金金额
   */
  usedAmount?: number | null;
  /**
   * 剩余可用押金金额（自动计算）
   */
  remainingAmount?: number | null;
  depositDate: string;
  /**
   * 押金到期日期（可选）
   */
  expiryDate?: string | null;
  /**
   * 押金使用日期
   */
  usedDate?: string | null;
  /**
   * 押金退还日期
   */
  refundDate?: string | null;
  /**
   * 押金的具体用途说明
   */
  purpose: string;
  /**
   * 押金相关的备注信息
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-interactions".
 */
export interface PatientInteraction {
  id: number;
  /**
   * 选择相关患者
   */
  patient: number | Patient;
  /**
   * 选择互动的类型
   */
  interactionType:
    | 'phone-call'
    | 'email'
    | 'consultation-note'
    | 'in-person-visit'
    | 'treatment-discussion'
    | 'billing-inquiry';
  /**
   * 处理此次互动的工作人员
   */
  staffMember: number | User;
  /**
   * 互动发生的时间
   */
  timestamp: string;
  /**
   * 简要描述此次互动的主题
   */
  title: string;
  /**
   * 详细记录互动内容、讨论要点等
   */
  notes: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * 记录互动的结果或解决方案
   */
  outcome?: string | null;
  /**
   * 此次互动是否需要后续跟进
   */
  followUpRequired?: boolean | null;
  /**
   * 计划跟进的日期
   */
  followUpDate?: string | null;
  /**
   * 此次互动的优先级
   */
  priority: 'low' | 'medium' | 'high';
  /**
   * 互动的当前状态
   */
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  /**
   * 如果此互动与特定预约相关，请选择
   */
  relatedAppointment?: (number | null) | Appointment;
  /**
   * 如果此互动与特定账单相关，请选择
   */
  relatedBill?: (number | null) | Bill;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-tasks".
 */
export interface PatientTask {
  id: number;
  /**
   * 选择相关患者
   */
  patient: number | Patient;
  /**
   * 简要描述任务内容
   */
  title: string;
  /**
   * 详细描述任务要求和注意事项
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * 选择任务的类型
   */
  taskType:
    | 'follow-up-call'
    | 'appointment-scheduling'
    | 'treatment-reminder'
    | 'billing-follow-up'
    | 'medical-record-update'
    | 'consultation-follow-up';
  /**
   * 负责执行此任务的工作人员
   */
  assignedTo: number | User;
  /**
   * 创建此任务的工作人员
   */
  createdBy: number | User;
  /**
   * 任务需要完成的日期
   */
  dueDate: string;
  /**
   * 任务的优先级
   */
  priority: 'low' | 'medium' | 'high' | 'urgent';
  /**
   * 任务的当前状态
   */
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  /**
   * 如果此任务基于特定互动创建，请选择
   */
  relatedInteraction?: (number | null) | PatientInteraction;
  /**
   * 任务完成的时间
   */
  completedAt?: string | null;
  /**
   * 任务完成时的备注和总结
   */
  completionNotes?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'treatments';
        value: number | Treatment;
      } | null)
    | ({
        relationTo: 'patients';
        value: number | Patient;
      } | null)
    | ({
        relationTo: 'appointments';
        value: number | Appointment;
      } | null)
    | ({
        relationTo: 'bills';
        value: number | Bill;
      } | null)
    | ({
        relationTo: 'bill-items';
        value: number | BillItem;
      } | null)
    | ({
        relationTo: 'payments';
        value: number | Payment;
      } | null)
    | ({
        relationTo: 'deposits';
        value: number | Deposit;
      } | null)
    | ({
        relationTo: 'patient-interactions';
        value: number | PatientInteraction;
      } | null)
    | ({
        relationTo: 'patient-tasks';
        value: number | PatientTask;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  role?: T;
  clerkId?: T;
  firstName?: T;
  lastName?: T;
  lastLogin?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "treatments_select".
 */
export interface TreatmentsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  defaultPrice?: T;
  defaultDurationInMinutes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patients_select".
 */
export interface PatientsSelect<T extends boolean = true> {
  userType?: T;
  status?: T;
  source?: T;
  referredBy?: T;
  fullName?: T;
  phone?: T;
  email?: T;
  photo?: T;
  convertedAt?: T;
  lastVisit?: T;
  emergencyContact?: T;
  allergies?:
    | T
    | {
        allergen?: T;
        severity?: T;
        id?: T;
      };
  medicalHistory?: T;
  medicalNotes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appointments_select".
 */
export interface AppointmentsSelect<T extends boolean = true> {
  appointmentType?: T;
  appointmentDate?: T;
  status?: T;
  treatment?: T;
  price?: T;
  durationInMinutes?: T;
  patient?: T;
  practitioner?: T;
  consultationType?: T;
  interestedTreatments?: T;
  paymentStatus?: T;
  outcome?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bills_select".
 */
export interface BillsSelect<T extends boolean = true> {
  billNumber?: T;
  patient?: T;
  appointment?: T;
  treatment?: T;
  billType?: T;
  status?: T;
  subtotal?: T;
  discountAmount?: T;
  taxAmount?: T;
  totalAmount?: T;
  paidAmount?: T;
  remainingAmount?: T;
  issueDate?: T;
  dueDate?: T;
  paidDate?: T;
  description?: T;
  notes?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bill-items_select".
 */
export interface BillItemsSelect<T extends boolean = true> {
  bill?: T;
  itemType?: T;
  itemId?: T;
  itemName?: T;
  description?: T;
  quantity?: T;
  unitPrice?: T;
  discountRate?: T;
  totalPrice?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments_select".
 */
export interface PaymentsSelect<T extends boolean = true> {
  paymentNumber?: T;
  bill?: T;
  patient?: T;
  amount?: T;
  paymentMethod?: T;
  paymentStatus?: T;
  transactionId?: T;
  paymentDate?: T;
  receivedBy?: T;
  relatedDeposit?: T;
  notes?: T;
  receiptNumber?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "deposits_select".
 */
export interface DepositsSelect<T extends boolean = true> {
  depositNumber?: T;
  patient?: T;
  appointment?: T;
  treatment?: T;
  depositType?: T;
  amount?: T;
  status?: T;
  usedAmount?: T;
  remainingAmount?: T;
  depositDate?: T;
  expiryDate?: T;
  usedDate?: T;
  refundDate?: T;
  purpose?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-interactions_select".
 */
export interface PatientInteractionsSelect<T extends boolean = true> {
  patient?: T;
  interactionType?: T;
  staffMember?: T;
  timestamp?: T;
  title?: T;
  notes?: T;
  outcome?: T;
  followUpRequired?: T;
  followUpDate?: T;
  priority?: T;
  status?: T;
  relatedAppointment?: T;
  relatedBill?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "patient-tasks_select".
 */
export interface PatientTasksSelect<T extends boolean = true> {
  patient?: T;
  title?: T;
  description?: T;
  taskType?: T;
  assignedTo?: T;
  createdBy?: T;
  dueDate?: T;
  priority?: T;
  status?: T;
  relatedInteraction?: T;
  completedAt?: T;
  completionNotes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}