import type { CollectionConfig } from 'payload'

export const PatientTasks: CollectionConfig = {
  slug: 'patient-tasks',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'patient', 'taskType', 'assignedTo', 'dueDate', 'priority', 'status'],
    group: 'CRM',
  },
  access: {
    // Read: All authenticated users can read task data based on role
    read: ({ req: { user } }) => {
      if (!user) return false;
      
      // Ad<PERSON> can read all tasks
      if (user.role === 'admin') return true;
      
      // Doctor can read tasks assigned to them or related to their patients
      if (user.role === 'doctor') {
        return {
          or: [
            {
              assignedTo: {
                equals: user.id,
              },
            },
            {
              createdBy: {
                equals: user.id,
              },
            },
            {
              taskType: {
                in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],
              },
            },
          ],
        };
      }
      
      // Front-desk can read scheduling and billing related tasks
      if (user.role === 'front-desk') {
        return {
          or: [
            {
              assignedTo: {
                equals: user.id,
              },
            },
            {
              taskType: {
                in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],
              },
            },
          ],
        };
      }
      
      return false;
    },

    // Create: Admin and staff can create tasks
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'doctor' || user.role === 'front-desk';
    },

    // Update: Users can update tasks assigned to them, Admin can update all
    update: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === 'admin') return true;
      
      // Staff can update tasks assigned to them or created by them
      return {
        or: [
          {
            assignedTo: {
              equals: user.id,
            },
          },
          {
            createdBy: {
              equals: user.id,
            },
          },
        ],
      };
    },

    // Delete: Only Admin can delete tasks
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  fields: [
    // 关联患者
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      label: '患者',
      admin: {
        description: '选择相关患者',
      },
    },

    // 任务标题
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
      label: '任务标题',
      admin: {
        description: '简要描述任务内容',
      },
    },

    // 任务描述
    {
      name: 'description',
      type: 'richText',
      label: '任务描述',
      admin: {
        description: '详细描述任务要求和注意事项',
      },
    },

    // 任务类型
    {
      name: 'taskType',
      type: 'select',
      required: true,
      options: [
        {
          label: '跟进电话',
          value: 'follow-up-call',
        },
        {
          label: '预约安排',
          value: 'appointment-scheduling',
        },
        {
          label: '治疗提醒',
          value: 'treatment-reminder',
        },
        {
          label: '账单跟进',
          value: 'billing-follow-up',
        },
        {
          label: '病历更新',
          value: 'medical-record-update',
        },
        {
          label: '咨询跟进',
          value: 'consultation-follow-up',
        },
      ],
      label: '任务类型',
      admin: {
        description: '选择任务的类型',
      },
    },

    // 分配给
    {
      name: 'assignedTo',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: '分配给',
      admin: {
        description: '负责执行此任务的工作人员',
      },
    },

    // 创建者
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: '创建者',
      admin: {
        description: '创建此任务的工作人员',
        readOnly: true,
      },
    },

    // 截止日期
    {
      name: 'dueDate',
      type: 'date',
      required: true,
      label: '截止日期',
      admin: {
        description: '任务需要完成的日期',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },

    // 优先级
    {
      name: 'priority',
      type: 'select',
      required: true,
      options: [
        {
          label: '低',
          value: 'low',
        },
        {
          label: '中',
          value: 'medium',
        },
        {
          label: '高',
          value: 'high',
        },
        {
          label: '紧急',
          value: 'urgent',
        },
      ],
      defaultValue: 'medium',
      label: '优先级',
      admin: {
        description: '任务的优先级',
      },
    },

    // 状态
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        {
          label: '待处理',
          value: 'pending',
        },
        {
          label: '进行中',
          value: 'in-progress',
        },
        {
          label: '已完成',
          value: 'completed',
        },
        {
          label: '已取消',
          value: 'cancelled',
        },
      ],
      defaultValue: 'pending',
      label: '状态',
      admin: {
        description: '任务的当前状态',
      },
    },

    // 关联互动（可选）
    {
      name: 'relatedInteraction',
      type: 'relationship',
      relationTo: 'patient-interactions',
      label: '关联互动',
      admin: {
        description: '如果此任务基于特定互动创建，请选择',
      },
    },

    // 完成时间
    {
      name: 'completedAt',
      type: 'date',
      label: '完成时间',
      admin: {
        description: '任务完成的时间',
        condition: (data) => data.status === 'completed',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },

    // 完成备注
    {
      name: 'completionNotes',
      type: 'richText',
      label: '完成备注',
      admin: {
        description: '任务完成时的备注和总结',
        condition: (data) => data.status === 'completed',
      },
    },
  ],
  hooks: {
    // 设置创建者和完成时间
    beforeChange: [
      async ({ data, operation, req }) => {
        // 设置创建者为当前用户
        if (operation === 'create' && req.user) {
          data.createdBy = req.user.id;
        }

        // 如果状态变为已完成，设置完成时间
        if (data.status === 'completed' && !data.completedAt) {
          data.completedAt = new Date();
        }

        // 如果状态从已完成变为其他状态，清除完成时间
        if (data.status !== 'completed') {
          data.completedAt = null;
          data.completionNotes = null;
        }

        return data;
      },
    ],
  },
}
