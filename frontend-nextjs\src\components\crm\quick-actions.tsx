'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  IconPhone, 
  IconMail, 
  IconMessageCircle, 
  IconCalendar,
  IconPlus,
  IconStethoscope,
  IconCreditCard,
  IconFileText,
  IconUser,
  IconChevronDown,
  IconClock
} from '@/components/icons';
import { Patient } from '@/types/clinic';
import { cn } from '@/lib/utils';

interface QuickActionsProps {
  patient: Patient;
  onCreateInteraction?: (type: string) => void;
  onCreateTask?: (type: string) => void;
  onScheduleAppointment?: () => void;
  onViewBilling?: () => void;
  className?: string;
  compact?: boolean;
}

const interactionActions = [
  {
    type: 'phone-call',
    label: '记录电话',
    description: '记录与患者的电话沟通',
    icon: IconPhone,
    color: 'text-blue-600 hover:text-blue-700',
    bgColor: 'hover:bg-blue-50',
  },
  {
    type: 'email',
    label: '记录邮件',
    description: '记录邮件沟通内容',
    icon: IconMail,
    color: 'text-green-600 hover:text-green-700',
    bgColor: 'hover:bg-green-50',
  },
  {
    type: 'consultation-note',
    label: '咨询记录',
    description: '添加咨询或诊疗记录',
    icon: IconStethoscope,
    color: 'text-purple-600 hover:text-purple-700',
    bgColor: 'hover:bg-purple-50',
  },
  {
    type: 'in-person-visit',
    label: '到院记录',
    description: '记录患者到院就诊',
    icon: IconUser,
    color: 'text-orange-600 hover:text-orange-700',
    bgColor: 'hover:bg-orange-50',
  },
  {
    type: 'treatment-discussion',
    label: '治疗讨论',
    description: '记录治疗方案讨论',
    icon: IconMessageCircle,
    color: 'text-indigo-600 hover:text-indigo-700',
    bgColor: 'hover:bg-indigo-50',
  },
  {
    type: 'billing-inquiry',
    label: '账单咨询',
    description: '记录账单相关咨询',
    icon: IconCreditCard,
    color: 'text-yellow-600 hover:text-yellow-700',
    bgColor: 'hover:bg-yellow-50',
  },
];

const taskActions = [
  {
    type: 'follow-up-call',
    label: '跟进电话',
    description: '安排跟进电话任务',
    icon: IconPhone,
    color: 'text-blue-600 hover:text-blue-700',
    bgColor: 'hover:bg-blue-50',
  },
  {
    type: 'appointment-scheduling',
    label: '预约安排',
    description: '安排预约相关任务',
    icon: IconCalendar,
    color: 'text-green-600 hover:text-green-700',
    bgColor: 'hover:bg-green-50',
  },
  {
    type: 'treatment-reminder',
    label: '治疗提醒',
    description: '创建治疗提醒任务',
    icon: IconStethoscope,
    color: 'text-purple-600 hover:text-purple-700',
    bgColor: 'hover:bg-purple-50',
  },
  {
    type: 'billing-follow-up',
    label: '账单跟进',
    description: '创建账单跟进任务',
    icon: IconCreditCard,
    color: 'text-yellow-600 hover:text-yellow-700',
    bgColor: 'hover:bg-yellow-50',
  },
  {
    type: 'medical-record-update',
    label: '病历更新',
    description: '安排病历更新任务',
    icon: IconFileText,
    color: 'text-indigo-600 hover:text-indigo-700',
    bgColor: 'hover:bg-indigo-50',
  },
  {
    type: 'consultation-follow-up',
    label: '咨询跟进',
    description: '创建咨询跟进任务',
    icon: IconMessageCircle,
    color: 'text-orange-600 hover:text-orange-700',
    bgColor: 'hover:bg-orange-50',
  },
];

export function QuickActions({
  patient,
  onCreateInteraction,
  onCreateTask,
  onScheduleAppointment,
  onViewBilling,
  className,
  compact = false,
}: QuickActionsProps) {
  const [isInteractionMenuOpen, setIsInteractionMenuOpen] = useState(false);
  const [isTaskMenuOpen, setIsTaskMenuOpen] = useState(false);

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {/* Quick Call Button */}
        <Button
          size="sm"
          variant="outline"
          onClick={() => onCreateInteraction?.('phone-call')}
          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        >
          <IconPhone className="size-4 mr-1" />
          电话
        </Button>

        {/* Quick Task Button */}
        <Button
          size="sm"
          variant="outline"
          onClick={() => onCreateTask?.('follow-up-call')}
          className="text-green-600 hover:text-green-700 hover:bg-green-50"
        >
          <IconClock className="size-4 mr-1" />
          任务
        </Button>

        {/* Schedule Appointment */}
        {onScheduleAppointment && (
          <Button
            size="sm"
            variant="outline"
            onClick={onScheduleAppointment}
            className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
          >
            <IconCalendar className="size-4 mr-1" />
            预约
          </Button>
        )}

        {/* More Actions Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm" variant="outline">
              <IconPlus className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>快速操作</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {onCreateInteraction && (
              <>
                <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                  添加互动记录
                </DropdownMenuLabel>
                {interactionActions.slice(0, 3).map((action) => {
                  const IconComponent = action.icon;
                  return (
                    <DropdownMenuItem
                      key={action.type}
                      onClick={() => onCreateInteraction(action.type)}
                      className={cn("cursor-pointer", action.bgColor)}
                    >
                      <IconComponent className={cn("size-4 mr-2", action.color)} />
                      {action.label}
                    </DropdownMenuItem>
                  );
                })}
                <DropdownMenuSeparator />
              </>
            )}

            {onCreateTask && (
              <>
                <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                  创建任务
                </DropdownMenuLabel>
                {taskActions.slice(0, 3).map((action) => {
                  const IconComponent = action.icon;
                  return (
                    <DropdownMenuItem
                      key={action.type}
                      onClick={() => onCreateTask(action.type)}
                      className={cn("cursor-pointer", action.bgColor)}
                    >
                      <IconComponent className={cn("size-4 mr-2", action.color)} />
                      {action.label}
                    </DropdownMenuItem>
                  );
                })}
              </>
            )}

            {onViewBilling && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={onViewBilling}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <IconCreditCard className="size-4 mr-2 text-gray-600" />
                  查看账单
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconPlus className="size-5" />
          快速操作
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          为 {patient.fullName} 执行常用操作
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Primary Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {/* Quick Call */}
          <Button
            variant="outline"
            onClick={() => onCreateInteraction?.('phone-call')}
            className="h-auto p-4 text-left justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
          >
            <div className="flex items-center gap-3">
              <IconPhone className="size-5" />
              <div>
                <div className="font-medium">联系患者</div>
                <div className="text-xs text-muted-foreground">记录电话沟通</div>
              </div>
            </div>
          </Button>

          {/* Schedule Appointment */}
          {onScheduleAppointment && (
            <Button
              variant="outline"
              onClick={onScheduleAppointment}
              className="h-auto p-4 text-left justify-start text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
            >
              <div className="flex items-center gap-3">
                <IconCalendar className="size-5" />
                <div>
                  <div className="font-medium">安排预约</div>
                  <div className="text-xs text-muted-foreground">预约治疗或咨询</div>
                </div>
              </div>
            </Button>
          )}
        </div>

        {/* Interaction Actions */}
        {onCreateInteraction && (
          <div>
            <DropdownMenu open={isInteractionMenuOpen} onOpenChange={setIsInteractionMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  <span className="flex items-center gap-2">
                    <IconMessageCircle className="size-4" />
                    添加互动记录
                  </span>
                  <IconChevronDown className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full min-w-[300px]">
                <DropdownMenuLabel>选择互动类型</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {interactionActions.map((action) => {
                  const IconComponent = action.icon;
                  return (
                    <DropdownMenuItem
                      key={action.type}
                      onClick={() => {
                        onCreateInteraction(action.type);
                        setIsInteractionMenuOpen(false);
                      }}
                      className={cn("cursor-pointer p-3", action.bgColor)}
                    >
                      <div className="flex items-start gap-3">
                        <IconComponent className={cn("size-4 mt-0.5", action.color)} />
                        <div>
                          <div className="font-medium">{action.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {action.description}
                          </div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Task Actions */}
        {onCreateTask && (
          <div>
            <DropdownMenu open={isTaskMenuOpen} onOpenChange={setIsTaskMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  <span className="flex items-center gap-2">
                    <IconClock className="size-4" />
                    创建跟进任务
                  </span>
                  <IconChevronDown className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full min-w-[300px]">
                <DropdownMenuLabel>选择任务类型</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {taskActions.map((action) => {
                  const IconComponent = action.icon;
                  return (
                    <DropdownMenuItem
                      key={action.type}
                      onClick={() => {
                        onCreateTask(action.type);
                        setIsTaskMenuOpen(false);
                      }}
                      className={cn("cursor-pointer p-3", action.bgColor)}
                    >
                      <div className="flex items-start gap-3">
                        <IconComponent className={cn("size-4 mt-0.5", action.color)} />
                        <div>
                          <div className="font-medium">{action.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {action.description}
                          </div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Additional Actions */}
        {onViewBilling && (
          <Button
            variant="outline"
            onClick={onViewBilling}
            className="w-full justify-start text-gray-600 hover:text-gray-700 hover:bg-gray-50"
          >
            <IconCreditCard className="size-4 mr-2" />
            查看账单记录
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
